# 3D Chess Game

A fully functional 3D chess game built with Three.js, featuring complete chess rules implementation, interactive 3D pieces, and comprehensive testing.

## Features

### Core Gameplay
- ✅ Complete chess rules implementation
- ✅ All piece movements (pawn, rook, knight, bishop, queen, king)
- ✅ Special moves (castling, en passant, pawn promotion)
- ✅ Check and checkmate detection
- ✅ Turn-based gameplay for two players
- ✅ Move validation and game state management

### 3D Visualization
- ✅ Interactive 3D chess board and pieces
- ✅ Realistic 3D piece models created with Three.js geometry
- ✅ Smooth piece movement animations
- ✅ Visual feedback for piece selection and valid moves
- ✅ Professional lighting and materials
- ✅ Responsive camera controls with orbit functionality

### User Interface
- ✅ Modern, responsive UI design
- ✅ Game status display with turn indicator
- ✅ Move history tracking
- ✅ Game controls (new game, undo, camera reset)
- ✅ Mobile-friendly touch controls
- ✅ Keyboard shortcuts support

### Technical Features
- ✅ WebGL-based 3D rendering with Three.js
- ✅ Raycasting for precise piece selection
- ✅ Cross-browser compatibility
- ✅ Performance optimizations
- ✅ Comprehensive test suite with Playwright

## Getting Started

### Prerequisites
- Modern web browser with WebGL support
- Python 3.x (for local server) or Node.js
- npm (for running tests)

### Installation

1. Clone or download the project files
2. Install test dependencies (optional):
   ```bash
   npm install
   ```

### Running the Game

#### Option 1: Python Server (Recommended)
```bash
python -m http.server 8000
```

#### Option 2: Node.js Server
```bash
npm run serve:node
```

#### Option 3: Any HTTP Server
Serve the files from any HTTP server on port 8000 or modify the base URL in tests.

### Opening the Game
Navigate to `http://localhost:8000` in your web browser.

## How to Play

### Basic Controls
- **Mouse/Touch**: Click to select pieces and make moves
- **Camera**: Drag to rotate view, scroll to zoom
- **Keyboard Shortcuts**:
  - `Ctrl+N`: New game
  - `Ctrl+Z`: Undo last move
  - `Ctrl+R`: Reset camera view

### Gameplay
1. White moves first
2. Click on a piece to select it (highlighted in red)
3. Valid moves are highlighted in green
4. Click on a valid square to move the piece
5. The game automatically detects check, checkmate, and stalemate

### Special Moves
- **Castling**: Select the king and click on the castling square
- **En Passant**: Automatically available when conditions are met
- **Pawn Promotion**: Pawns automatically promote to queens (can be extended)

## Testing

### Running Tests
```bash
# Run all tests
npm test

# Run tests with browser UI
npm run test:ui

# Run tests in headed mode (visible browser)
npm run test:headed

# Debug tests
npm run test:debug
```

### Test Coverage
- ✅ Game initialization and UI functionality
- ✅ Chess rules and move validation
- ✅ 3D scene and interaction testing
- ✅ Cross-browser compatibility
- ✅ Performance and responsiveness
- ✅ Error handling and edge cases

## Project Structure

```
3d-chess-game/
├── index.html              # Main HTML file
├── js/
│   ├── main.js             # Application entry point
│   ├── scene.js            # 3D scene management
│   ├── pieces.js           # 3D piece models
│   ├── chess-engine.js     # Chess rules and logic
│   └── interaction.js      # User input handling
├── tests/
│   ├── chess-game.spec.js  # Basic game functionality
│   ├── chess-rules.spec.js # Chess rules testing
│   ├── 3d-interaction.spec.js # 3D scene testing
│   └── cross-browser.spec.js  # Browser compatibility
├── package.json            # Dependencies and scripts
├── playwright.config.js    # Test configuration
└── README.md              # This file
```

## Architecture

### Chess Engine (`chess-engine.js`)
- Implements complete chess rules
- Move validation and generation
- Game state management
- Check/checkmate detection

### 3D Scene (`scene.js`)
- Three.js scene setup and management
- Camera and lighting configuration
- Board and piece positioning
- Animation system

### Piece Models (`pieces.js`)
- 3D geometry creation for all piece types
- Material application and positioning
- Initial board setup

### Interaction System (`interaction.js`)
- Mouse and touch event handling
- Raycasting for 3D object selection
- Move execution and visual feedback

### Main Controller (`main.js`)
- Application initialization
- UI event handling
- Game state coordination

## Browser Support

### Tested Browsers
- ✅ Chrome/Chromium (Desktop & Mobile)
- ✅ Firefox (Desktop & Mobile)
- ✅ Safari/WebKit (Desktop & Mobile)
- ✅ Edge

### Requirements
- WebGL support (available in all modern browsers)
- ES6+ JavaScript support
- Canvas API support

## Performance Optimizations

- Efficient 3D rendering with optimized geometries
- Smart raycasting with object filtering
- Smooth animations with easing functions
- Responsive design with viewport adaptation
- Memory management for 3D objects

## Known Limitations

- Pawn promotion currently defaults to queen (can be extended with UI)
- No AI opponent (human vs human only)
- No network multiplayer support
- No game saving/loading functionality

## Future Enhancements

- AI opponent with difficulty levels
- Online multiplayer support
- Game analysis and move suggestions
- Custom piece sets and board themes
- Tournament mode and time controls
- Game notation export (PGN format)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

MIT License - feel free to use and modify for your projects.

## Acknowledgments

- Three.js for the excellent 3D graphics library
- Playwright for comprehensive testing capabilities
- Chess programming community for rules reference
