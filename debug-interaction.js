const { chromium } = require('playwright');

async function debugInteraction() {
    console.log('🔍 Debugging interaction system...');
    
    const browser = await chromium.launch({ headless: false });
    const page = await browser.newPage();
    
    // Capture all console messages
    page.on('console', msg => {
        const type = msg.type();
        const text = msg.text();
        const icon = type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
        console.log(`${icon} [CONSOLE] ${text}`);
    });
    
    try {
        await page.goto('http://localhost:8000/');
        
        // Wait for game to load
        await page.waitForSelector('#loadingScreen', { state: 'hidden', timeout: 20000 });
        console.log('✅ Game loaded');
        
        // Check interaction system state
        const interactionState = await page.evaluate(() => {
            try {
                const result = {
                    interactionExists: !!(window.chessGame && window.chessGame.interaction),
                    sceneExists: !!(window.chessGame && window.chessGame.scene),
                    piecesMapSize: 0,
                    boardSquaresLength: 0,
                    canvasExists: document.getElementById('gameCanvas') !== null,
                    firstPieceHasUserData: false,
                    firstPieceUserDataType: 'none',
                    firstPieceUserDataPosition: 'none'
                };
                
                if (window.chessGame && window.chessGame.scene) {
                    result.piecesMapSize = window.chessGame.scene.pieces ? window.chessGame.scene.pieces.size : 0;
                    result.boardSquaresLength = window.chessGame.scene.boardSquares ? window.chessGame.scene.boardSquares.length : 0;
                    
                    if (window.chessGame.scene.pieces && window.chessGame.scene.pieces.size > 0) {
                        const firstPiece = Array.from(window.chessGame.scene.pieces.values())[0];
                        if (firstPiece) {
                            result.firstPieceHasUserData = !!firstPiece.userData;
                            result.firstPieceUserDataType = firstPiece.userData?.type || 'none';
                            result.firstPieceUserDataPosition = firstPiece.userData?.position || 'none';
                        }
                    }
                }
                
                return result;
            } catch (error) {
                return { error: error.message };
            }
        });
        
        console.log('\n📊 Interaction System State:');
        Object.entries(interactionState).forEach(([key, value]) => {
            console.log(`   ${key}: ${JSON.stringify(value, null, 2)}`);
        });
        
        // Force trigger a click and check what happens
        console.log('\n🖱️ Testing direct click with detailed logging...');
        
        await page.evaluate(() => {
            // Add a temporary click listener to see if events are firing
            const canvas = document.getElementById('gameCanvas');
            
            canvas.addEventListener('click', function tempClickHandler(e) {
                console.log('🎯 Canvas click detected at:', e.clientX, e.clientY);
                
                if (window.chessGame && window.chessGame.interaction) {
                    console.log('🎯 Interaction system exists, calling handleClick manually...');
                    
                    // Update mouse position manually
                    const rect = canvas.getBoundingClientRect();
                    window.chessGame.interaction.mouse.x = ((e.clientX - rect.left) / rect.width) * 2 - 1;
                    window.chessGame.interaction.mouse.y = -((e.clientY - rect.top) / rect.height) * 2 + 1;
                    
                    console.log('🎯 Mouse coordinates:', window.chessGame.interaction.mouse);
                    
                    // Call handleClick directly
                    window.chessGame.interaction.handleClick();
                } else {
                    console.log('❌ Interaction system not found');
                }
                
                // Remove this temporary handler
                canvas.removeEventListener('click', tempClickHandler);
            });
        });
        
        // Click on the canvas
        await page.click('#gameCanvas', { position: { x: 400, y: 400 } });
        
        await page.waitForTimeout(2000);
        
        console.log('\n👁️ Browser will stay open for 10 seconds for manual testing...');
        await page.waitForTimeout(10000);
        
    } catch (error) {
        console.error(`💥 Debug failed: ${error.message}`);
    } finally {
        await browser.close();
    }
}

debugInteraction().catch(console.error);