const { chromium } = require('playwright');

(async () => {
  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();
  
  // Listen for console messages
  page.on('console', msg => {
    console.log('CONSOLE:', msg.type(), msg.text());
  });
  
  // Listen for errors
  page.on('pageerror', error => {
    console.log('PAGE ERROR:', error.message);
  });
  
  try {
    console.log('Navigating to game...');
    await page.goto('http://localhost:8000');
    
    console.log('Waiting for canvas...');
    await page.waitForSelector('#gameCanvas', { timeout: 10000 });
    
    console.log('Checking loading screen...');
    const loadingScreen = await page.locator('#loadingScreen');
    const isVisible = await loadingScreen.isVisible();
    console.log('Loading screen visible:', isVisible);
    
    if (isVisible) {
      console.log('Waiting for loading screen to disappear...');
      await page.waitForTimeout(20000); // Wait 20 seconds
      
      const stillVisible = await loadingScreen.isVisible();
      console.log('Loading screen still visible after 20s:', stillVisible);
      
      if (stillVisible) {
        const content = await loadingScreen.innerHTML();
        console.log('Loading screen content:', content);
      }
    }
    
    console.log('Taking screenshot...');
    await page.screenshot({ path: 'debug-screenshot.png' });
    
  } catch (error) {
    console.error('Error:', error);
  }
  
  await browser.close();
})();
