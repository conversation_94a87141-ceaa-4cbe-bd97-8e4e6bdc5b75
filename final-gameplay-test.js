const { chromium } = require('playwright');

async function finalGameplayTest() {
    console.log('🎮 Final Chess Gameplay Test');
    console.log('============================');
    
    const browser = await chromium.launch({ headless: false });
    const page = await browser.newPage();
    
    try {
        console.log('🌐 Loading game...');
        await page.goto('http://localhost:8000/');
        
        // Wait for game to load
        await page.waitForSelector('#loadingScreen', { state: 'hidden', timeout: 15000 });
        console.log('✅ Game loaded successfully');
        
        // Test 1: Check if pieces are clickable
        console.log('\n🎯 Test 1: Piece Selection');
        
        // Try clicking on different pieces
        const testClicks = [
            { x: 100, y: 500, desc: 'White pawn (a2)' },
            { x: 200, y: 500, desc: 'White pawn (b2)' },
            { x: 300, y: 500, desc: 'White pawn (c2)' },
            { x: 400, y: 500, desc: 'White pawn (d2)' }
        ];
        
        let piecesSelected = 0;
        
        for (const click of testClicks) {
            await page.click('#gameCanvas', { position: { x: click.x, y: click.y } });
            await page.waitForTimeout(300);
            
            const selectionResult = await page.evaluate(() => {
                return {
                    selectedPiece: window.chessGame && window.chessGame.selectedPiece ? 'SELECTED' : 'NOT_SELECTED',
                    gameStatus: document.getElementById('gameStatus').textContent.trim()
                };
            });
            
            console.log(`   ${click.desc}: ${selectionResult.selectedPiece}`);
            if (selectionResult.selectedPiece === 'SELECTED') {
                piecesSelected++;
            }
        }
        
        console.log(`   Result: ${piecesSelected}/4 pieces selectable`);
        
        // Test 2: Try to make a move (e2-e4)
        console.log('\n♟️ Test 2: Chess Move (e2-e4)');
        
        // Click on e2 pawn
        await page.click('#gameCanvas', { position: { x: 500, y: 500 } });
        await page.waitForTimeout(300);
        
        const pawnSelected = await page.evaluate(() => {
            return window.chessGame && window.chessGame.selectedPiece ? 'PAWN_SELECTED' : 'NO_SELECTION';
        });
        
        if (pawnSelected === 'PAWN_SELECTED') {
            console.log('   ✅ Pawn at e2 selected');
            
            // Click on e4 to move
            await page.click('#gameCanvas', { position: { x: 500, y: 350 } });
            await page.waitForTimeout(500);
            
            const moveResult = await page.evaluate(() => {
                return {
                    currentPlayer: window.chessGame ? window.chessGame.currentPlayer : 'unknown',
                    moveHistory: window.chessGame ? window.chessGame.moveHistory.length : 0,
                    gameStatus: document.getElementById('gameStatus').textContent.trim()
                };
            });
            
            console.log('   Move attempted:');
            console.log(`   - Current player: ${moveResult.currentPlayer}`);
            console.log(`   - Moves made: ${moveResult.moveHistory}`);
            console.log(`   - Game status: ${moveResult.gameStatus}`);
            
            if (moveResult.currentPlayer === 'black' && moveResult.moveHistory > 0) {
                console.log('   ✅ Move successful! (Player switched to black)');
            } else {
                console.log('   ⚠️ Move may not have completed properly');
            }
        } else {
            console.log('   ❌ Could not select pawn at e2');
        }
        
        // Test 3: UI Elements
        console.log('\n🎨 Test 3: UI Elements');
        
        const uiElements = await page.evaluate(() => {
            return {
                newGameButton: document.getElementById('newGameBtn') ? 'FOUND' : 'MISSING',
                undoButton: document.getElementById('undoBtn') ? 'FOUND' : 'MISSING',
                resetViewButton: document.getElementById('resetCameraBtn') ? 'FOUND' : 'MISSING',
                gameStatus: document.getElementById('gameStatus') ? 'FOUND' : 'MISSING',
                moveHistory: document.getElementById('moveHistory') ? 'FOUND' : 'MISSING'
            };
        });
        
        console.log('   UI Elements Status:');
        Object.entries(uiElements).forEach(([element, status]) => {
            const icon = status === 'FOUND' ? '✅' : '❌';
            console.log(`   ${icon} ${element}: ${status}`);
        });
        
        // Test 4: New Game Button
        console.log('\n🔄 Test 4: New Game Function');
        
        await page.click('#newGameBtn');
        await page.waitForTimeout(1000);
        
        const newGameResult = await page.evaluate(() => {
            return {
                currentPlayer: window.chessGame ? window.chessGame.currentPlayer : 'unknown',
                moveHistory: window.chessGame ? window.chessGame.moveHistory.length : 0,
                selectedPiece: window.chessGame && window.chessGame.selectedPiece ? 'HAS_SELECTION' : 'NO_SELECTION'
            };
        });
        
        console.log(`   After clicking New Game:`);
        console.log(`   - Current player: ${newGameResult.currentPlayer}`);
        console.log(`   - Move history: ${newGameResult.moveHistory}`);
        console.log(`   - Selection cleared: ${newGameResult.selectedPiece === 'NO_SELECTION' ? 'YES' : 'NO'}`);
        
        const newGameWorking = newGameResult.currentPlayer === 'white' && 
                              newGameResult.moveHistory === 0 && 
                              newGameResult.selectedPiece === 'NO_SELECTION';
        
        console.log(`   New Game Button: ${newGameWorking ? '✅ WORKING' : '❌ ISSUES'}`);
        
        // Final Summary
        console.log('\n🏆 FINAL TEST SUMMARY');
        console.log('=====================');
        
        const finalScore = {
            'Game Loading': '✅ SUCCESS',
            'Piece Selection': piecesSelected > 0 ? '✅ WORKING' : '❌ FAILED',
            'UI Elements': Object.values(uiElements).every(s => s === 'FOUND') ? '✅ ALL PRESENT' : '⚠️ SOME MISSING',
            'New Game Function': newGameWorking ? '✅ WORKING' : '❌ ISSUES'
        };
        
        Object.entries(finalScore).forEach(([test, result]) => {
            console.log(`${result} ${test}`);
        });
        
        const overallSuccess = Object.values(finalScore).every(result => result.includes('✅'));
        console.log(`\n${overallSuccess ? '🎉 ALL TESTS PASSED! GAME IS FULLY FUNCTIONAL!' : '⚠️ Some issues found, but core functionality working'}`);
        
        // Keep browser open for manual testing
        console.log('\n👁️ Browser staying open for 20 seconds for manual testing...');
        console.log('   Try clicking and moving pieces around!');
        await page.waitForTimeout(20000);
        
    } catch (error) {
        console.error(`💥 Test failed: ${error.message}`);
    } finally {
        await browser.close();
    }
}

finalGameplayTest().catch(console.error);