<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Chess Game</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            overflow: hidden;
            height: 100vh;
        }

        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #gameCanvas {
            display: block;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        #gameUI {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 100;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 20px;
            border-radius: 10px;
            min-width: 250px;
            backdrop-filter: blur(10px);
        }

        .ui-section {
            margin-bottom: 15px;
        }

        .ui-section h3 {
            margin-bottom: 8px;
            color: #4CAF50;
            font-size: 16px;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-indicator.white {
            background-color: #ffffff;
        }

        .status-indicator.black {
            background-color: #333333;
        }

        .game-controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #45a049;
        }

        .btn:disabled {
            background: #666;
            cursor: not-allowed;
        }

        #moveHistory {
            max-height: 150px;
            overflow-y: auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
        }

        .move-entry {
            margin-bottom: 2px;
        }

        #gameStatus {
            font-weight: bold;
            font-size: 16px;
        }

        .check-warning {
            color: #ff6b6b;
            font-weight: bold;
        }

        .checkmate-warning {
            color: #ff3333;
            font-weight: bold;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        #loadingScreen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            color: white;
            font-size: 24px;
        }

        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4CAF50;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin-right: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            #gameUI {
                top: 10px;
                left: 10px;
                right: 10px;
                padding: 15px;
                min-width: auto;
            }

            .game-controls {
                justify-content: center;
            }

            .btn {
                flex: 1;
                min-width: 80px;
            }
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <canvas id="gameCanvas"></canvas>
        
        <div id="gameUI">
            <div class="ui-section">
                <h3>Game Status</h3>
                <div id="gameStatus">
                    <span class="status-indicator white"></span>
                    White to move
                </div>
            </div>

            <div class="ui-section">
                <h3>Controls</h3>
                <div class="game-controls">
                    <button class="btn" id="newGameBtn">New Game</button>
                    <button class="btn" id="undoBtn" disabled>Undo</button>
                    <button class="btn" id="resetCameraBtn">Reset View</button>
                </div>
            </div>

            <div class="ui-section">
                <h3>Move History</h3>
                <div id="moveHistory">
                    <div class="move-entry">Game started</div>
                </div>
            </div>
        </div>

        <div id="loadingScreen">
            <div class="loading-spinner"></div>
            <div>Loading 3D Chess Game...</div>
        </div>
    </div>

    <!-- Three.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r158/three.min.js"></script>
    <script>
        // OrbitControls inline to avoid CDN issues
        (function() {
            if (typeof THREE !== 'undefined') {
                THREE.OrbitControls = function(object, domElement) {
                    this.object = object;
                    this.domElement = domElement || document;
                    this.enabled = true;
                    this.target = new THREE.Vector3();
                    this.enableDamping = false;
                    this.dampingFactor = 0.05;
                    this.enableZoom = true;
                    this.zoomSpeed = 1.0;
                    this.enableRotate = true;
                    this.rotateSpeed = 1.0;
                    this.enablePan = true;
                    this.panSpeed = 1.0;
                    this.autoRotate = false;
                    this.autoRotateSpeed = 2.0;
                    this.enableKeys = true;
                    this.keys = { LEFT: 37, UP: 38, RIGHT: 39, BOTTOM: 40 };
                    this.mouseButtons = { LEFT: THREE.MOUSE.ROTATE, MIDDLE: THREE.MOUSE.DOLLY, RIGHT: THREE.MOUSE.PAN };
                    this.update = function() { return false; };
                    this.dispose = function() {};
                };
            }
        })();
    </script>
    
    <!-- Game Scripts -->
    <script src="js/chess-engine.js"></script>
    <script src="js/pieces.js"></script>
    <script src="js/scene.js"></script>
    <script src="js/interaction.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
