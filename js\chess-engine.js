/**
 * Chess Engine
 * Implements chess rules, move validation, and game state management
 */

class ChessEngine {
    constructor() {
        this.board = this.createInitialBoard();
        this.currentPlayer = 'white';
        this.moveHistory = [];
        this.gameState = {
            check: false,
            checkmate: false,
            stalemate: false,
            draw: false
        };
        
        // Special move tracking
        this.castlingRights = {
            white: { kingside: true, queenside: true },
            black: { kingside: true, queenside: true }
        };
        this.enPassantTarget = null;
        this.halfMoveClock = 0;
        this.fullMoveNumber = 1;
    }

    createInitialBoard() {
        const board = Array(8).fill(null).map(() => Array(8).fill(null));
        
        // Place pieces in starting positions
        const initialSetup = [
            ['r', 'n', 'b', 'q', 'k', 'b', 'n', 'r'], // Black back rank
            ['p', 'p', 'p', 'p', 'p', 'p', 'p', 'p'], // Black pawns
            [null, null, null, null, null, null, null, null],
            [null, null, null, null, null, null, null, null],
            [null, null, null, null, null, null, null, null],
            [null, null, null, null, null, null, null, null],
            ['P', 'P', 'P', 'P', 'P', 'P', 'P', 'P'], // White pawns
            ['R', 'N', 'B', 'Q', 'K', 'B', 'N', 'R']  // White back rank
        ];
        
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 8; col++) {
                const piece = initialSetup[row][col];
                if (piece) {
                    board[row][col] = {
                        type: piece.toLowerCase(),
                        color: piece === piece.toUpperCase() ? 'white' : 'black',
                        hasMoved: false
                    };
                }
            }
        }
        
        return board;
    }

    resetGame() {
        this.board = this.createInitialBoard();
        this.currentPlayer = 'white';
        this.moveHistory = [];
        this.gameState = {
            check: false,
            checkmate: false,
            stalemate: false,
            draw: false
        };
        this.castlingRights = {
            white: { kingside: true, queenside: true },
            black: { kingside: true, queenside: true }
        };
        this.enPassantTarget = null;
        this.halfMoveClock = 0;
        this.fullMoveNumber = 1;
        
        console.log('Chess engine reset');
    }

    positionToCoords(position) {
        const file = position.charCodeAt(0) - 97; // a=0, b=1, etc.
        const rank = 8 - parseInt(position[1]); // 8=0, 7=1, etc.
        return { row: rank, col: file };
    }

    coordsToPosition(row, col) {
        const file = String.fromCharCode(97 + col);
        const rank = (8 - row).toString();
        return file + rank;
    }

    getPieceAt(position) {
        const { row, col } = this.positionToCoords(position);
        return this.board[row][col];
    }

    setPieceAt(position, piece) {
        const { row, col } = this.positionToCoords(position);
        this.board[row][col] = piece;
    }

    isValidPosition(position) {
        if (typeof position !== 'string' || position.length !== 2) return false;
        const file = position[0];
        const rank = position[1];
        return file >= 'a' && file <= 'h' && rank >= '1' && rank <= '8';
    }

    isSquareEmpty(position) {
        return this.getPieceAt(position) === null;
    }

    isSquareOccupiedByOpponent(position, color) {
        const piece = this.getPieceAt(position);
        return piece !== null && piece.color !== color;
    }

    isSquareOccupiedByFriend(position, color) {
        const piece = this.getPieceAt(position);
        return piece !== null && piece.color === color;
    }

    getValidMoves(position) {
        const piece = this.getPieceAt(position);
        if (!piece || piece.color !== this.currentPlayer) {
            return [];
        }

        let moves = [];
        
        switch (piece.type) {
            case 'p':
                moves = this.getPawnMoves(position, piece.color);
                break;
            case 'r':
                moves = this.getRookMoves(position, piece.color);
                break;
            case 'n':
                moves = this.getKnightMoves(position, piece.color);
                break;
            case 'b':
                moves = this.getBishopMoves(position, piece.color);
                break;
            case 'q':
                moves = this.getQueenMoves(position, piece.color);
                break;
            case 'k':
                moves = this.getKingMoves(position, piece.color);
                break;
        }

        // Filter out moves that would leave the king in check
        return moves.filter(move => !this.wouldLeaveKingInCheck(position, move, piece.color));
    }

    getPawnMoves(position, color) {
        const moves = [];
        const { row, col } = this.positionToCoords(position);
        const direction = color === 'white' ? -1 : 1;
        const startRow = color === 'white' ? 6 : 1;

        // Forward move
        const oneSquareForward = this.coordsToPosition(row + direction, col);
        if (this.isValidPosition(oneSquareForward) && this.isSquareEmpty(oneSquareForward)) {
            moves.push(oneSquareForward);

            // Two squares forward from starting position
            if (row === startRow) {
                const twoSquaresForward = this.coordsToPosition(row + 2 * direction, col);
                if (this.isValidPosition(twoSquaresForward) && this.isSquareEmpty(twoSquaresForward)) {
                    moves.push(twoSquaresForward);
                }
            }
        }

        // Captures
        const captureLeft = this.coordsToPosition(row + direction, col - 1);
        const captureRight = this.coordsToPosition(row + direction, col + 1);

        if (this.isValidPosition(captureLeft) && this.isSquareOccupiedByOpponent(captureLeft, color)) {
            moves.push(captureLeft);
        }
        if (this.isValidPosition(captureRight) && this.isSquareOccupiedByOpponent(captureRight, color)) {
            moves.push(captureRight);
        }

        // En passant
        if (this.enPassantTarget) {
            if (captureLeft === this.enPassantTarget || captureRight === this.enPassantTarget) {
                moves.push(this.enPassantTarget);
            }
        }

        return moves;
    }

    getRookMoves(position, color) {
        return this.getSlidingMoves(position, color, [
            [-1, 0], [1, 0], [0, -1], [0, 1] // Vertical and horizontal
        ]);
    }

    getBishopMoves(position, color) {
        return this.getSlidingMoves(position, color, [
            [-1, -1], [-1, 1], [1, -1], [1, 1] // Diagonals
        ]);
    }

    getQueenMoves(position, color) {
        return this.getSlidingMoves(position, color, [
            [-1, 0], [1, 0], [0, -1], [0, 1], // Rook moves
            [-1, -1], [-1, 1], [1, -1], [1, 1] // Bishop moves
        ]);
    }

    getSlidingMoves(position, color, directions) {
        const moves = [];
        const { row, col } = this.positionToCoords(position);

        for (const [dRow, dCol] of directions) {
            for (let i = 1; i < 8; i++) {
                const newRow = row + i * dRow;
                const newCol = col + i * dCol;
                const newPosition = this.coordsToPosition(newRow, newCol);

                if (!this.isValidPosition(newPosition)) break;

                if (this.isSquareEmpty(newPosition)) {
                    moves.push(newPosition);
                } else if (this.isSquareOccupiedByOpponent(newPosition, color)) {
                    moves.push(newPosition);
                    break; // Can't move past an opponent piece
                } else {
                    break; // Can't move past a friendly piece
                }
            }
        }

        return moves;
    }

    getKnightMoves(position, color) {
        const moves = [];
        const { row, col } = this.positionToCoords(position);
        const knightMoves = [
            [-2, -1], [-2, 1], [-1, -2], [-1, 2],
            [1, -2], [1, 2], [2, -1], [2, 1]
        ];

        for (const [dRow, dCol] of knightMoves) {
            const newPosition = this.coordsToPosition(row + dRow, col + dCol);
            if (this.isValidPosition(newPosition) && 
                (this.isSquareEmpty(newPosition) || this.isSquareOccupiedByOpponent(newPosition, color))) {
                moves.push(newPosition);
            }
        }

        return moves;
    }

    getKingMoves(position, color) {
        const moves = [];
        const { row, col } = this.positionToCoords(position);
        const kingMoves = [
            [-1, -1], [-1, 0], [-1, 1],
            [0, -1],           [0, 1],
            [1, -1],  [1, 0],  [1, 1]
        ];

        for (const [dRow, dCol] of kingMoves) {
            const newPosition = this.coordsToPosition(row + dRow, col + dCol);
            if (this.isValidPosition(newPosition) && 
                (this.isSquareEmpty(newPosition) || this.isSquareOccupiedByOpponent(newPosition, color))) {
                moves.push(newPosition);
            }
        }

        // Castling
        if (this.canCastle(color, 'kingside')) {
            moves.push(color === 'white' ? 'g1' : 'g8');
        }
        if (this.canCastle(color, 'queenside')) {
            moves.push(color === 'white' ? 'c1' : 'c8');
        }

        return moves;
    }

    canCastle(color, side) {
        if (!this.castlingRights[color][side]) return false;
        
        const king = this.getPieceAt(color === 'white' ? 'e1' : 'e8');
        if (!king || king.hasMoved) return false;

        const rookPosition = color === 'white' 
            ? (side === 'kingside' ? 'h1' : 'a1')
            : (side === 'kingside' ? 'h8' : 'a8');
        
        const rook = this.getPieceAt(rookPosition);
        if (!rook || rook.hasMoved) return false;

        // Check if squares between king and rook are empty
        const squares = color === 'white'
            ? (side === 'kingside' ? ['f1', 'g1'] : ['b1', 'c1', 'd1'])
            : (side === 'kingside' ? ['f8', 'g8'] : ['b8', 'c8', 'd8']);

        for (const square of squares) {
            if (!this.isSquareEmpty(square)) return false;
        }

        // Check if king is in check or would pass through check
        const kingPosition = color === 'white' ? 'e1' : 'e8';
        if (this.isSquareUnderAttack(kingPosition, color === 'white' ? 'black' : 'white')) {
            return false;
        }

        const passingSquares = color === 'white'
            ? (side === 'kingside' ? ['f1', 'g1'] : ['c1', 'd1'])
            : (side === 'kingside' ? ['f8', 'g8'] : ['c8', 'd8']);

        for (const square of passingSquares) {
            if (this.isSquareUnderAttack(square, color === 'white' ? 'black' : 'white')) {
                return false;
            }
        }

        return true;
    }

    wouldLeaveKingInCheck(fromPosition, toPosition, color) {
        // Make a temporary move
        const originalPiece = this.getPieceAt(toPosition);
        const movingPiece = this.getPieceAt(fromPosition);
        
        this.setPieceAt(toPosition, movingPiece);
        this.setPieceAt(fromPosition, null);

        // Find king position
        let kingPosition = null;
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 8; col++) {
                const piece = this.board[row][col];
                if (piece && piece.type === 'k' && piece.color === color) {
                    kingPosition = this.coordsToPosition(row, col);
                    break;
                }
            }
            if (kingPosition) break;
        }

        const inCheck = kingPosition && this.isSquareUnderAttack(kingPosition, color === 'white' ? 'black' : 'white');

        // Restore the board
        this.setPieceAt(fromPosition, movingPiece);
        this.setPieceAt(toPosition, originalPiece);

        return inCheck;
    }

    isSquareUnderAttack(position, byColor) {
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 8; col++) {
                const piece = this.board[row][col];
                if (piece && piece.color === byColor) {
                    const attackerPosition = this.coordsToPosition(row, col);
                    const moves = this.getPieceAttacks(attackerPosition, piece);
                    if (moves.includes(position)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    getPieceAttacks(position, piece) {
        // Similar to getValidMoves but doesn't check for leaving king in check
        // and includes all squares the piece attacks (even if occupied by friendly pieces)
        switch (piece.type) {
            case 'p':
                return this.getPawnAttacks(position, piece.color);
            case 'r':
                return this.getRookMoves(position, piece.color);
            case 'n':
                return this.getKnightMoves(position, piece.color);
            case 'b':
                return this.getBishopMoves(position, piece.color);
            case 'q':
                return this.getQueenMoves(position, piece.color);
            case 'k':
                return this.getKingAttacks(position, piece.color);
            default:
                return [];
        }
    }

    getPawnAttacks(position, color) {
        const attacks = [];
        const { row, col } = this.positionToCoords(position);
        const direction = color === 'white' ? -1 : 1;

        const captureLeft = this.coordsToPosition(row + direction, col - 1);
        const captureRight = this.coordsToPosition(row + direction, col + 1);

        if (this.isValidPosition(captureLeft)) attacks.push(captureLeft);
        if (this.isValidPosition(captureRight)) attacks.push(captureRight);

        return attacks;
    }

    getKingAttacks(position, color) {
        const attacks = [];
        const { row, col } = this.positionToCoords(position);
        const kingMoves = [
            [-1, -1], [-1, 0], [-1, 1],
            [0, -1],           [0, 1],
            [1, -1],  [1, 0],  [1, 1]
        ];

        for (const [dRow, dCol] of kingMoves) {
            const newPosition = this.coordsToPosition(row + dRow, col + dCol);
            if (this.isValidPosition(newPosition)) {
                attacks.push(newPosition);
            }
        }

        return attacks;
    }

    makeMove(fromPosition, toPosition, piece) {
        if (!this.isValidPosition(fromPosition) || !this.isValidPosition(toPosition)) {
            return { valid: false, reason: 'Invalid position' };
        }

        const movingPiece = this.getPieceAt(fromPosition);
        if (!movingPiece) {
            return { valid: false, reason: 'No piece at source position' };
        }

        if (movingPiece.color !== this.currentPlayer) {
            return { valid: false, reason: 'Not your piece' };
        }

        const validMoves = this.getValidMoves(fromPosition);
        if (!validMoves.includes(toPosition)) {
            return { valid: false, reason: 'Invalid move' };
        }

        // Execute the move
        const capturedPiece = this.getPieceAt(toPosition);
        const moveResult = {
            valid: true,
            from: fromPosition,
            to: toPosition,
            piece: movingPiece,
            captured: capturedPiece,
            special: null,
            promotion: null,
            notation: this.getMoveNotation(fromPosition, toPosition, movingPiece, capturedPiece)
        };

        // Check for pawn promotion
        if (movingPiece.type === 'p') {
            const { row } = this.positionToCoords(toPosition);
            if ((movingPiece.color === 'white' && row === 0) || (movingPiece.color === 'black' && row === 7)) {
                moveResult.special = 'promotion';
                moveResult.promotion = 'q'; // Default to queen, can be changed by UI
            }
        }

        // Handle special moves
        if (movingPiece.type === 'k' && Math.abs(this.positionToCoords(fromPosition).col - this.positionToCoords(toPosition).col) === 2) {
            // Castling
            moveResult.special = 'castling';
            this.executeCastling(fromPosition, toPosition);
        } else if (movingPiece.type === 'p' && toPosition === this.enPassantTarget) {
            // En passant
            moveResult.special = 'enPassant';
            this.executeEnPassant(fromPosition, toPosition);
        } else {
            // Regular move
            this.setPieceAt(toPosition, movingPiece);
            this.setPieceAt(fromPosition, null);

            // Handle pawn promotion
            if (moveResult.special === 'promotion') {
                movingPiece.type = moveResult.promotion;
                moveResult.notation += '=' + moveResult.promotion.toUpperCase();
            }
        }

        // Mark piece as moved
        movingPiece.hasMoved = true;

        // Update castling rights
        this.updateCastlingRights(fromPosition, toPosition, movingPiece);

        // Update en passant target
        this.updateEnPassantTarget(fromPosition, toPosition, movingPiece);

        // Update move counters
        this.updateMoveCounters(movingPiece, capturedPiece);

        // Switch players
        this.currentPlayer = this.currentPlayer === 'white' ? 'black' : 'white';

        // Update game state
        this.updateGameState();

        // Add to move history
        this.moveHistory.push(moveResult);

        return moveResult;
    }

    executeCastling(fromPosition, toPosition) {
        const piece = this.getPieceAt(fromPosition);
        const { row } = this.positionToCoords(fromPosition);
        const isKingside = this.positionToCoords(toPosition).col > this.positionToCoords(fromPosition).col;

        // Move king
        this.setPieceAt(toPosition, piece);
        this.setPieceAt(fromPosition, null);

        // Move rook
        if (isKingside) {
            const rookFrom = this.coordsToPosition(row, 7);
            const rookTo = this.coordsToPosition(row, 5);
            const rook = this.getPieceAt(rookFrom);
            this.setPieceAt(rookTo, rook);
            this.setPieceAt(rookFrom, null);
            rook.hasMoved = true;
        } else {
            const rookFrom = this.coordsToPosition(row, 0);
            const rookTo = this.coordsToPosition(row, 3);
            const rook = this.getPieceAt(rookFrom);
            this.setPieceAt(rookTo, rook);
            this.setPieceAt(rookFrom, null);
            rook.hasMoved = true;
        }
    }

    executeEnPassant(fromPosition, toPosition) {
        const piece = this.getPieceAt(fromPosition);
        const { row: fromRow } = this.positionToCoords(fromPosition);
        const { col: toCol } = this.positionToCoords(toPosition);

        // Move pawn
        this.setPieceAt(toPosition, piece);
        this.setPieceAt(fromPosition, null);

        // Remove captured pawn
        const capturedPawnPosition = this.coordsToPosition(fromRow, toCol);
        this.setPieceAt(capturedPawnPosition, null);
    }

    updateCastlingRights(fromPosition, toPosition, piece) {
        const color = piece.color;

        // King moved
        if (piece.type === 'k') {
            this.castlingRights[color].kingside = false;
            this.castlingRights[color].queenside = false;
        }

        // Rook moved
        if (piece.type === 'r') {
            if (fromPosition === (color === 'white' ? 'a1' : 'a8')) {
                this.castlingRights[color].queenside = false;
            } else if (fromPosition === (color === 'white' ? 'h1' : 'h8')) {
                this.castlingRights[color].kingside = false;
            }
        }

        // Rook captured
        if (toPosition === 'a1') this.castlingRights.white.queenside = false;
        if (toPosition === 'h1') this.castlingRights.white.kingside = false;
        if (toPosition === 'a8') this.castlingRights.black.queenside = false;
        if (toPosition === 'h8') this.castlingRights.black.kingside = false;
    }

    updateEnPassantTarget(fromPosition, toPosition, piece) {
        this.enPassantTarget = null;

        if (piece.type === 'p') {
            const { row: fromRow } = this.positionToCoords(fromPosition);
            const { row: toRow, col } = this.positionToCoords(toPosition);

            // Pawn moved two squares
            if (Math.abs(fromRow - toRow) === 2) {
                const targetRow = (fromRow + toRow) / 2;
                this.enPassantTarget = this.coordsToPosition(targetRow, col);
            }
        }
    }

    updateMoveCounters(movingPiece, capturedPiece) {
        if (movingPiece.type === 'p' || capturedPiece) {
            this.halfMoveClock = 0;
        } else {
            this.halfMoveClock++;
        }

        if (this.currentPlayer === 'black') {
            this.fullMoveNumber++;
        }
    }

    updateGameState() {
        const opponentColor = this.currentPlayer === 'white' ? 'black' : 'white';

        // Find opponent's king
        let kingPosition = null;
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 8; col++) {
                const piece = this.board[row][col];
                if (piece && piece.type === 'k' && piece.color === this.currentPlayer) {
                    kingPosition = this.coordsToPosition(row, col);
                    break;
                }
            }
            if (kingPosition) break;
        }

        // Check if current player is in check
        this.gameState.check = kingPosition && this.isSquareUnderAttack(kingPosition, opponentColor);

        // Check for checkmate or stalemate
        const hasValidMoves = this.hasValidMoves(this.currentPlayer);

        if (!hasValidMoves) {
            if (this.gameState.check) {
                this.gameState.checkmate = true;
            } else {
                this.gameState.stalemate = true;
            }
        }

        // Check for draw conditions
        this.gameState.draw = this.halfMoveClock >= 50 || this.isInsufficientMaterial() || this.isThreefoldRepetition();
    }

    hasValidMoves(color) {
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 8; col++) {
                const piece = this.board[row][col];
                if (piece && piece.color === color) {
                    const position = this.coordsToPosition(row, col);
                    const moves = this.getValidMoves(position);
                    if (moves.length > 0) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    isInsufficientMaterial() {
        const pieces = [];
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 8; col++) {
                const piece = this.board[row][col];
                if (piece) {
                    pieces.push(piece.type);
                }
            }
        }

        // King vs King
        if (pieces.length === 2) return true;

        // King and Bishop/Knight vs King
        if (pieces.length === 3) {
            return pieces.includes('b') || pieces.includes('n');
        }

        return false;
    }

    isThreefoldRepetition() {
        // Simplified implementation - would need full position tracking in a real game
        return false;
    }

    getMoveNotation(from, to, piece, captured) {
        let notation = '';

        if (piece.type === 'k' && Math.abs(this.positionToCoords(from).col - this.positionToCoords(to).col) === 2) {
            // Castling
            return this.positionToCoords(to).col > this.positionToCoords(from).col ? 'O-O' : 'O-O-O';
        }

        // Piece symbol (except for pawns)
        if (piece.type !== 'p') {
            notation += piece.type.toUpperCase();
        }

        // Capture notation
        if (captured || (piece.type === 'p' && this.positionToCoords(from).col !== this.positionToCoords(to).col)) {
            if (piece.type === 'p') {
                notation += from[0]; // File of the pawn
            }
            notation += 'x';
        }

        notation += to;

        return notation;
    }

    undoMove(moveData) {
        if (this.moveHistory.length === 0) return false;

        // Remove from history
        this.moveHistory.pop();

        // Switch player back
        this.currentPlayer = this.currentPlayer === 'white' ? 'black' : 'white';

        // Restore pieces
        this.setPieceAt(moveData.from, moveData.piece);
        this.setPieceAt(moveData.to, moveData.captured);

        // Mark piece as not moved if it was the first move
        if (this.moveHistory.length === 0 || !this.moveHistory.some(move => move.from === moveData.from || move.to === moveData.from)) {
            moveData.piece.hasMoved = false;
        }

        // Handle special moves
        if (moveData.special === 'castling') {
            this.undoCastling(moveData.from, moveData.to);
        } else if (moveData.special === 'enPassant') {
            this.undoEnPassant(moveData);
        }

        // Restore game state (simplified)
        this.gameState = {
            check: false,
            checkmate: false,
            stalemate: false,
            draw: false
        };

        return true;
    }

    undoCastling(from, to) {
        const { row } = this.positionToCoords(from);
        const isKingside = this.positionToCoords(to).col > this.positionToCoords(from).col;

        if (isKingside) {
            const rookFrom = this.coordsToPosition(row, 5);
            const rookTo = this.coordsToPosition(row, 7);
            const rook = this.getPieceAt(rookFrom);
            this.setPieceAt(rookTo, rook);
            this.setPieceAt(rookFrom, null);
        } else {
            const rookFrom = this.coordsToPosition(row, 3);
            const rookTo = this.coordsToPosition(row, 0);
            const rook = this.getPieceAt(rookFrom);
            this.setPieceAt(rookTo, rook);
            this.setPieceAt(rookFrom, null);
        }
    }

    undoEnPassant(moveData) {
        // Restore captured pawn
        const { row: fromRow } = this.positionToCoords(moveData.from);
        const { col: toCol } = this.positionToCoords(moveData.to);
        const capturedPawnPosition = this.coordsToPosition(fromRow, toCol);

        this.setPieceAt(capturedPawnPosition, {
            type: 'p',
            color: moveData.piece.color === 'white' ? 'black' : 'white',
            hasMoved: true
        });
    }

    getGameState() {
        return { ...this.gameState };
    }
}
