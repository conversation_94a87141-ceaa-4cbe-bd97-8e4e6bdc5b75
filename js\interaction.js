/**
 * Chess Interaction System
 * Handles mouse/touch input, raycasting, and piece selection
 */

class ChessInteraction {
    constructor(scene, chessEngine, gameController) {
        this.scene = scene;
        this.chessEngine = chessEngine;
        this.gameController = gameController;
        
        // Raycasting
        this.raycaster = new THREE.Raycaster();
        this.mouse = new THREE.Vector2();
        
        // Interaction state
        this.selectedPiece = null;
        this.selectedPosition = null;
        this.validMoves = [];
        this.isInteractionEnabled = true;
        
        // Event handlers
        this.onMouseMove = this.onMouseMove.bind(this);
        this.onMouseClick = this.onMouseClick.bind(this);
        this.onTouchStart = this.onTouchStart.bind(this);
        this.onTouchMove = this.onTouchMove.bind(this);
        this.onTouchEnd = this.onTouchEnd.bind(this);
        
        // Touch handling
        this.touchStartTime = 0;
        this.touchMoved = false;
    }

    init() {
        this.setupEventListeners();
        console.log('Chess interaction system initialized');
    }

    setupEventListeners() {
        const canvas = this.scene.canvas;
        
        // Mouse events
        canvas.addEventListener('mousemove', this.onMouseMove, false);
        canvas.addEventListener('click', this.onMouseClick, false);
        
        // Touch events
        canvas.addEventListener('touchstart', this.onTouchStart, { passive: false });
        canvas.addEventListener('touchmove', this.onTouchMove, { passive: false });
        canvas.addEventListener('touchend', this.onTouchEnd, { passive: false });
        
        // Prevent context menu
        canvas.addEventListener('contextmenu', (event) => {
            event.preventDefault();
        });
    }

    removeEventListeners() {
        const canvas = this.scene.canvas;
        
        canvas.removeEventListener('mousemove', this.onMouseMove);
        canvas.removeEventListener('click', this.onMouseClick);
        canvas.removeEventListener('touchstart', this.onTouchStart);
        canvas.removeEventListener('touchmove', this.onTouchMove);
        canvas.removeEventListener('touchend', this.onTouchEnd);
    }

    onMouseMove(event) {
        if (!this.isInteractionEnabled) return;
        
        this.updateMousePosition(event);
        this.handleHover();
    }

    onMouseClick(event) {
        if (!this.isInteractionEnabled) return;
        
        event.preventDefault();
        this.updateMousePosition(event);
        this.handleClick();
    }

    onTouchStart(event) {
        if (!this.isInteractionEnabled) return;
        
        event.preventDefault();
        this.touchStartTime = Date.now();
        this.touchMoved = false;
        
        if (event.touches.length === 1) {
            this.updateMousePositionFromTouch(event.touches[0]);
        }
    }

    onTouchMove(event) {
        if (!this.isInteractionEnabled) return;
        
        event.preventDefault();
        this.touchMoved = true;
        
        if (event.touches.length === 1) {
            this.updateMousePositionFromTouch(event.touches[0]);
            this.handleHover();
        }
    }

    onTouchEnd(event) {
        if (!this.isInteractionEnabled) return;
        
        event.preventDefault();
        
        const touchDuration = Date.now() - this.touchStartTime;
        
        // Treat as click if touch was short and didn't move much
        if (touchDuration < 500 && !this.touchMoved) {
            this.handleClick();
        }
    }

    updateMousePosition(event) {
        const rect = this.scene.canvas.getBoundingClientRect();
        
        this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
    }

    updateMousePositionFromTouch(touch) {
        const rect = this.scene.canvas.getBoundingClientRect();
        
        this.mouse.x = ((touch.clientX - rect.left) / rect.width) * 2 - 1;
        this.mouse.y = -((touch.clientY - rect.top) / rect.height) * 2 + 1;
    }

    handleHover() {
        const intersectedObject = this.getIntersectedObject();
        
        // Update cursor style
        if (intersectedObject) {
            if (intersectedObject.userData.type === 'piece') {
                const piece = this.chessEngine.getPieceAt(intersectedObject.userData.position);
                if (piece && piece.color === this.gameController.getCurrentPlayer()) {
                    this.scene.canvas.style.cursor = 'pointer';
                } else {
                    this.scene.canvas.style.cursor = 'default';
                }
            } else if (intersectedObject.userData.type === 'square') {
                if (this.selectedPiece && this.validMoves.includes(intersectedObject.userData.position)) {
                    this.scene.canvas.style.cursor = 'pointer';
                } else {
                    this.scene.canvas.style.cursor = 'default';
                }
            }
        } else {
            this.scene.canvas.style.cursor = 'default';
        }
    }

    handleClick() {
        const intersectedObject = this.getIntersectedObject();
        
        if (!intersectedObject) {
            this.clearSelection();
            return;
        }

        if (intersectedObject.userData.type === 'piece') {
            this.handlePieceClick(intersectedObject);
        } else if (intersectedObject.userData.type === 'square') {
            this.handleSquareClick(intersectedObject);
        }
    }

    handlePieceClick(pieceObject) {
        const position = pieceObject.userData.position;
        const piece = this.chessEngine.getPieceAt(position);
        
        if (!piece) {
            this.clearSelection();
            return;
        }

        // If clicking on opponent's piece while having a piece selected, try to capture
        if (this.selectedPiece && piece.color !== this.gameController.getCurrentPlayer()) {
            if (this.validMoves.includes(position)) {
                this.makeMove(this.selectedPosition, position);
            } else {
                this.clearSelection();
            }
            return;
        }

        // If clicking on own piece
        if (piece.color === this.gameController.getCurrentPlayer()) {
            if (this.selectedPosition === position) {
                // Clicking on already selected piece - deselect
                this.clearSelection();
            } else {
                // Select new piece
                this.selectPiece(position, piece);
            }
        } else {
            this.clearSelection();
        }
    }

    handleSquareClick(squareObject) {
        const position = squareObject.userData.position;
        
        if (this.selectedPiece && this.validMoves.includes(position)) {
            // Make move to empty square
            this.makeMove(this.selectedPosition, position);
        } else {
            // Clear selection if clicking on invalid square
            this.clearSelection();
        }
    }

    selectPiece(position, piece) {
        // Clear previous selection
        this.clearSelection();
        
        // Set new selection
        this.selectedPosition = position;
        this.selectedPiece = piece;
        this.gameController.setSelectedPiece(piece);
        
        // Get valid moves
        this.validMoves = this.chessEngine.getValidMoves(position);
        
        // Highlight selected piece and valid moves
        this.scene.highlightSelectedPiece(position);
        this.scene.highlightValidMoves(this.validMoves);
        
        console.log(`Selected ${piece.color} ${piece.type} at ${position}`, this.validMoves);
    }

    clearSelection() {
        this.selectedPiece = null;
        this.selectedPosition = null;
        this.validMoves = [];
        this.gameController.setSelectedPiece(null);
        
        // Clear highlights
        this.scene.clearHighlights();
        
        // Reset cursor
        this.scene.canvas.style.cursor = 'default';
    }

    makeMove(fromPosition, toPosition) {
        if (this.gameController.isGameOver()) {
            this.clearSelection();
            return;
        }

        // Attempt to make the move
        const moveResult = this.gameController.makeMove(fromPosition, toPosition, this.selectedPiece);
        
        if (moveResult) {
            // Move was successful
            this.scene.movePiece(fromPosition, toPosition, true);
            
            // Handle special moves in 3D scene
            this.handleSpecialMoveVisualization(moveResult);
            
            console.log(`Move made: ${fromPosition} -> ${toPosition}`);
        } else {
            console.log(`Invalid move: ${fromPosition} -> ${toPosition}`);
        }
        
        // Clear selection regardless of move success
        this.clearSelection();
    }

    handleSpecialMoveVisualization(moveResult) {
        if (!moveResult.special) return;
        
        switch (moveResult.special) {
            case 'castling':
                // Move the rook as well
                if (moveResult.to === 'g1') {
                    this.scene.movePiece('h1', 'f1', true);
                } else if (moveResult.to === 'c1') {
                    this.scene.movePiece('a1', 'd1', true);
                } else if (moveResult.to === 'g8') {
                    this.scene.movePiece('h8', 'f8', true);
                } else if (moveResult.to === 'c8') {
                    this.scene.movePiece('a8', 'd8', true);
                }
                break;
                
            case 'enPassant':
                // Remove the captured pawn
                const capturedPawnRow = moveResult.piece.color === 'white' ? '5' : '4';
                const capturedPawnPosition = moveResult.to[0] + capturedPawnRow;
                this.scene.removePieceAt(capturedPawnPosition);
                break;
        }
    }

    getIntersectedObject() {
        // Update raycaster
        this.raycaster.setFromCamera(this.mouse, this.scene.camera);
        
        // Get all objects that can be intersected
        const intersectableObjects = [];
        
        // Add pieces
        this.scene.pieces.forEach(piece => {
            intersectableObjects.push(piece);
        });
        
        // Add board squares
        intersectableObjects.push(...this.scene.boardSquares);
        
        // Perform raycasting
        const intersects = this.raycaster.intersectObjects(intersectableObjects, true);
        
        if (intersects.length > 0) {
            // Find the topmost object with userData
            for (const intersect of intersects) {
                let object = intersect.object;
                
                // Traverse up the hierarchy to find object with userData
                while (object && !object.userData.type) {
                    object = object.parent;
                }
                
                if (object && object.userData.type) {
                    return object;
                }
            }
        }
        
        return null;
    }

    setInteractionEnabled(enabled) {
        this.isInteractionEnabled = enabled;
        
        if (!enabled) {
            this.clearSelection();
            this.scene.canvas.style.cursor = 'default';
        }
    }

    reset() {
        this.clearSelection();
        this.setInteractionEnabled(true);
        console.log('Interaction system reset');
    }

    dispose() {
        this.removeEventListeners();
        this.clearSelection();
        console.log('Interaction system disposed');
    }
}
