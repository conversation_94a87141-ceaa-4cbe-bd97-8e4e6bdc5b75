/**
 * 3D Chess Game - Main Application
 * Entry point for the chess game application
 */

class ChessGame {
    constructor() {
        this.scene = null;
        this.chessEngine = null;
        this.interaction = null;
        this.isInitialized = false;
        
        // Game state
        this.currentPlayer = 'white';
        this.selectedPiece = null;
        this.gameOver = false;
        this.moveHistory = [];
        
        // UI elements
        this.gameStatusEl = document.getElementById('gameStatus');
        this.moveHistoryEl = document.getElementById('moveHistory');
        this.loadingScreenEl = document.getElementById('loadingScreen');
        this.newGameBtn = document.getElementById('newGameBtn');
        this.undoBtn = document.getElementById('undoBtn');
        this.resetCameraBtn = document.getElementById('resetCameraBtn');
        
        this.init();
    }

    async init() {
        try {
            console.log('=== CHESS GAME INITIALIZATION START ===');
            console.log('Step 1: Starting initialization...');
            
            // Test THREE.js availability
            console.log('Step 2: Testing THREE.js availability...');
            if (typeof THREE === 'undefined') {
                throw new Error('THREE.js library is not loaded');
            }
            console.log('✓ THREE.js is available, version:', THREE.REVISION);
            
            // Test OrbitControls availability
            console.log('Step 3: Testing OrbitControls availability...');
            if (typeof THREE.OrbitControls === 'undefined') {
                throw new Error('THREE.OrbitControls is not loaded');
            }
            console.log('✓ OrbitControls is available');
            
            // Test WebGL support
            console.log('Step 4: Testing WebGL support...');
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            if (!gl) {
                throw new Error('WebGL is not supported by this browser');
            }
            console.log('✓ WebGL is supported');
            
            // Initialize chess engine
            console.log('Step 5: Initializing chess engine...');
            try {
                this.chessEngine = new ChessEngine();
                console.log('✓ Chess engine initialized');
            } catch (engineError) {
                console.error('✗ Failed to initialize chess engine:', engineError);
                throw new Error(`Chess engine initialization failed: ${engineError.message}`);
            }
            
            // Initialize 3D scene
            console.log('Step 6: Initializing 3D scene...');
            try {
                this.scene = new ChessScene('gameCanvas');
                await this.scene.init();
                console.log('✓ 3D scene initialized');
            } catch (sceneError) {
                console.error('✗ Failed to initialize 3D scene:', sceneError);
                throw new Error(`3D scene initialization failed: ${sceneError.message}`);
            }
            
            // Initialize interaction system
            console.log('Step 7: Initializing interaction system...');
            try {
                this.interaction = new ChessInteraction(this.scene, this.chessEngine, this);
                this.interaction.init();
                console.log('✓ Interaction system initialized');
            } catch (interactionError) {
                console.error('✗ Failed to initialize interaction system:', interactionError);
                throw new Error(`Interaction system initialization failed: ${interactionError.message}`);
            }
            
            // Set up event listeners
            console.log('Step 8: Setting up event listeners...');
            try {
                this.setupEventListeners();
                console.log('✓ Event listeners set up');
            } catch (eventError) {
                console.error('✗ Failed to set up event listeners:', eventError);
                throw new Error(`Event listener setup failed: ${eventError.message}`);
            }
            
            // Start the game
            console.log('Step 9: Starting new game...');
            try {
                this.startNewGame();
                console.log('✓ New game started');
            } catch (gameError) {
                console.error('✗ Failed to start new game:', gameError);
                throw new Error(`Game start failed: ${gameError.message}`);
            }
            
            // Hide loading screen
            console.log('Step 10: Hiding loading screen...');
            try {
                this.hideLoadingScreen();
                console.log('✓ Loading screen hidden');
            } catch (loadingError) {
                console.error('✗ Failed to hide loading screen:', loadingError);
                throw new Error(`Loading screen hide failed: ${loadingError.message}`);
            }
            
            this.isInitialized = true;
            console.log('=== CHESS GAME INITIALIZATION COMPLETE ===');
            console.log('✓ 3D Chess Game initialized successfully!');
            
        } catch (error) {
            console.error('=== CHESS GAME INITIALIZATION FAILED ===');
            console.error('Error details:', {
                message: error.message,
                stack: error.stack,
                name: error.name
            });
            this.showError(`Failed to load the game: ${error.message}`);
        }
    }

    setupEventListeners() {
        // New game button
        this.newGameBtn.addEventListener('click', () => {
            this.startNewGame();
        });

        // Undo button
        this.undoBtn.addEventListener('click', () => {
            this.undoLastMove();
        });

        // Reset camera button
        this.resetCameraBtn.addEventListener('click', () => {
            this.scene.resetCamera();
        });

        // Window resize
        window.addEventListener('resize', () => {
            if (this.scene) {
                this.scene.handleResize();
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (event) => {
            this.handleKeyboard(event);
        });
    }

    handleKeyboard(event) {
        switch (event.key) {
            case 'n':
            case 'N':
                if (event.ctrlKey) {
                    event.preventDefault();
                    this.startNewGame();
                }
                break;
            case 'z':
            case 'Z':
                if (event.ctrlKey) {
                    event.preventDefault();
                    this.undoLastMove();
                }
                break;
            case 'r':
            case 'R':
                if (event.ctrlKey) {
                    event.preventDefault();
                    this.scene.resetCamera();
                }
                break;
        }
    }

    startNewGame() {
        console.log('Starting new game...');
        
        // Reset game state
        this.currentPlayer = 'white';
        this.selectedPiece = null;
        this.gameOver = false;
        this.moveHistory = [];
        
        // Reset chess engine
        this.chessEngine.resetGame();
        
        // Reset 3D scene and set up initial position
        if (this.scene) {
            this.scene.resetBoard();
            this.scene.setupInitialPosition();
        }
        
        // Reset interaction
        if (this.interaction) {
            this.interaction.reset();
        }
        
        // Update UI
        this.updateGameStatus();
        this.updateMoveHistory();
        this.updateButtons();
        
        console.log('New game started!');
    }

    makeMove(from, to, piece) {
        if (this.gameOver) return false;
        
        try {
            // Validate move with chess engine
            const moveResult = this.chessEngine.makeMove(from, to, piece);
            
            if (moveResult.valid) {
                // Record move in history
                this.moveHistory.push({
                    from,
                    to,
                    piece,
                    captured: moveResult.captured,
                    special: moveResult.special,
                    notation: moveResult.notation,
                    timestamp: Date.now()
                });
                
                // Switch players
                this.currentPlayer = this.currentPlayer === 'white' ? 'black' : 'white';
                
                // Check for game end conditions
                this.checkGameEnd();
                
                // Update UI
                this.updateGameStatus();
                this.updateMoveHistory();
                this.updateButtons();
                
                console.log(`Move made: ${moveResult.notation}`);
                return true;
            } else {
                console.log('Invalid move attempted');
                return false;
            }
        } catch (error) {
            console.error('Error making move:', error);
            return false;
        }
    }

    undoLastMove() {
        if (this.moveHistory.length === 0 || this.gameOver) return;
        
        try {
            const lastMove = this.moveHistory.pop();
            
            // Undo move in chess engine
            this.chessEngine.undoMove(lastMove);
            
            // Switch back player
            this.currentPlayer = this.currentPlayer === 'white' ? 'black' : 'white';
            
            // Update 3D scene
            this.scene.undoMove(lastMove);
            
            // Reset game over state
            this.gameOver = false;
            
            // Update UI
            this.updateGameStatus();
            this.updateMoveHistory();
            this.updateButtons();
            
            console.log('Move undone');
        } catch (error) {
            console.error('Error undoing move:', error);
        }
    }

    checkGameEnd() {
        const gameState = this.chessEngine.getGameState();
        
        if (gameState.checkmate) {
            this.gameOver = true;
            console.log(`Checkmate! ${this.currentPlayer === 'white' ? 'Black' : 'White'} wins!`);
        } else if (gameState.stalemate) {
            this.gameOver = true;
            console.log('Stalemate! Game is a draw.');
        } else if (gameState.draw) {
            this.gameOver = true;
            console.log('Draw!');
        }
    }

    updateGameStatus() {
        const gameState = this.chessEngine.getGameState();
        let statusText = '';
        let statusClass = '';
        
        if (this.gameOver) {
            if (gameState.checkmate) {
                statusText = `Checkmate! ${this.currentPlayer === 'white' ? 'Black' : 'White'} wins!`;
                statusClass = 'checkmate-warning';
            } else if (gameState.stalemate) {
                statusText = 'Stalemate - Draw!';
                statusClass = 'check-warning';
            } else if (gameState.draw) {
                statusText = 'Draw!';
                statusClass = 'check-warning';
            }
        } else if (gameState.check) {
            statusText = `${this.currentPlayer.charAt(0).toUpperCase() + this.currentPlayer.slice(1)} in check!`;
            statusClass = 'check-warning';
        } else {
            statusText = `${this.currentPlayer.charAt(0).toUpperCase() + this.currentPlayer.slice(1)} to move`;
        }
        
        this.gameStatusEl.innerHTML = `
            <span class="status-indicator ${this.currentPlayer}"></span>
            <span class="${statusClass}">${statusText}</span>
        `;
    }

    updateMoveHistory() {
        this.moveHistoryEl.innerHTML = '';
        
        if (this.moveHistory.length === 0) {
            this.moveHistoryEl.innerHTML = '<div class="move-entry">Game started</div>';
            return;
        }
        
        this.moveHistory.forEach((move, index) => {
            const moveNumber = Math.floor(index / 2) + 1;
            const isWhiteMove = index % 2 === 0;
            
            if (isWhiteMove) {
                this.moveHistoryEl.innerHTML += `<div class="move-entry">${moveNumber}. ${move.notation}`;
            } else {
                this.moveHistoryEl.innerHTML += ` ${move.notation}</div>`;
            }
        });
        
        // Scroll to bottom
        this.moveHistoryEl.scrollTop = this.moveHistoryEl.scrollHeight;
    }

    updateButtons() {
        this.undoBtn.disabled = this.moveHistory.length === 0 || this.gameOver;
    }

    hideLoadingScreen() {
        // Fade out loading screen for smooth transition
        this.loadingScreenEl.style.transition = 'opacity 0.5s ease-out';
        this.loadingScreenEl.style.opacity = '0';

        setTimeout(() => {
            this.loadingScreenEl.style.display = 'none';
        }, 500);
    }

    showError(message) {
        console.error('🚨 Showing error to user:', message);
        
        this.loadingScreenEl.innerHTML = `
            <div style="text-align: center; color: white; font-family: Arial, sans-serif;">
                <div style="color: #ff6b6b; font-size: 32px; margin-bottom: 20px;">⚠️ Error</div>
                <div style="font-size: 18px; margin-bottom: 20px; max-width: 600px; margin-left: auto; margin-right: auto; line-height: 1.5;">${message}</div>
                <div style="margin-bottom: 30px;">
                    <button class="btn" onclick="location.reload()" style="margin: 5px; padding: 12px 24px; background: #4CAF50; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;">🔄 Reload Page</button>
                    <button class="btn" onclick="window.open('debug.html', '_blank')" style="margin: 5px; padding: 12px 24px; background: #2196F3; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;">🔍 Debug Mode</button>
                </div>
                <details style="text-align: left; max-width: 800px; margin: 0 auto; background: rgba(0,0,0,0.3); padding: 20px; border-radius: 8px;">
                    <summary style="cursor: pointer; font-weight: bold; margin-bottom: 10px;">🛠️ Troubleshooting Tips</summary>
                    <ul style="line-height: 1.6; padding-left: 20px;">
                        <li>Check that your browser supports WebGL (Chrome, Firefox, Safari recommended)</li>
                        <li>Try refreshing the page or clearing browser cache</li>
                        <li>Open Debug Mode for detailed error analysis</li>
                        <li>Check browser console (F12) for detailed error messages</li>
                        <li>Ensure JavaScript is enabled in your browser</li>
                        <li>Try disabling browser extensions temporarily</li>
                    </ul>
                </details>
            </div>
        `;
    }

    // Public API for interaction system
    getCurrentPlayer() {
        return this.currentPlayer;
    }

    isGameOver() {
        return this.gameOver;
    }

    getSelectedPiece() {
        return this.selectedPiece;
    }

    setSelectedPiece(piece) {
        this.selectedPiece = piece;
    }
}

// Pre-initialization environment check with retry
function checkEnvironment(attempt = 1) {
    console.log(`🔍 Pre-initialization environment check (attempt ${attempt})...`);
    
    const issues = [];
    
    // Check THREE.js
    if (typeof THREE === 'undefined') {
        issues.push('THREE.js library not loaded');
    }
    
    // Check OrbitControls
    if (typeof THREE !== 'undefined' && typeof THREE.OrbitControls === 'undefined') {
        issues.push('OrbitControls not loaded');
    }
    
    // Check WebGL
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    if (!gl) {
        issues.push('WebGL not supported');
    }
    
    // Check required classes with detailed debugging
    const requiredClasses = ['ChessEngine', 'ChessPieces', 'ChessScene', 'ChessInteraction'];
    requiredClasses.forEach(className => {
        const classExists = typeof window[className] !== 'undefined';
        const globalExists = typeof eval(className) !== 'undefined';
        
        console.log(`🔍 Checking ${className}: window.${className} = ${classExists}, global = ${globalExists}`);
        
        if (!classExists && !globalExists) {
            issues.push(`${className} class not loaded`);
        }
    });
    
    // Check DOM elements
    const requiredElements = ['gameCanvas', 'gameStatus', 'moveHistory', 'loadingScreen'];
    requiredElements.forEach(elementId => {
        if (!document.getElementById(elementId)) {
            issues.push(`Required element '${elementId}' not found`);
        }
    });
    
    if (issues.length > 0) {
        console.error(`✗ Environment check failed (attempt ${attempt}):`);
        issues.forEach(issue => console.error(`  - ${issue}`));
        
        // Retry up to 3 times with increasing delays
        if (attempt < 3) {
            const retryDelay = attempt * 500; // 500ms, 1000ms, 1500ms
            console.log(`🔄 Retrying environment check in ${retryDelay}ms...`);
            setTimeout(() => {
                if (checkEnvironment(attempt + 1)) {
                    initializeGame();
                }
            }, retryDelay);
            return false;
        }
        
        // Final failure after all retries
        console.error('🚨 Environment check failed after 3 attempts');
        
        // Show environment error
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            loadingScreen.innerHTML = `
                <div style="text-align: center; color: white; font-family: Arial, sans-serif;">
                    <div style="color: #ff6b6b; font-size: 32px; margin-bottom: 20px;">⚠️ Environment Error</div>
                    <div style="font-size: 18px; margin-bottom: 20px;">
                        The following issues prevent the game from starting:
                    </div>
                    <ul style="text-align: left; max-width: 500px; margin: 0 auto 20px auto; line-height: 1.6;">
                        ${issues.map(issue => `<li>${issue}</li>`).join('')}
                    </ul>
                    <button onclick="location.reload()" style="margin: 5px; padding: 12px 24px; background: #4CAF50; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;">🔄 Retry</button>
                    <button onclick="window.open('debug.html', '_blank')" style="margin: 5px; padding: 12px 24px; background: #2196F3; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;">🔍 Debug Mode</button>
                </div>
            `;
        }
        
        return false;
    }
    
    console.log('✓ Environment check passed - all systems ready');
    return true;
}

// Initialize the game instance
function initializeGame() {
    console.log('🎮 Creating ChessGame instance...');
    try {
        window.chessGame = new ChessGame();
    } catch (error) {
        console.error('🚨 Failed to create ChessGame instance:', error);
        
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            loadingScreen.innerHTML = `
                <div style="text-align: center; color: white;">
                    <div style="color: #ff6b6b; font-size: 24px; margin-bottom: 20px;">⚠️ Initialization Error</div>
                    <div>Failed to create game instance: ${error.message}</div>
                    <button onclick="location.reload()" style="margin: 20px 5px 5px 5px; padding: 12px 24px; background: #4CAF50; color: white; border: none; border-radius: 5px; cursor: pointer;">🔄 Reload</button>
                    <button onclick="window.open('debug.html', '_blank')" style="margin: 20px 5px 5px 5px; padding: 12px 24px; background: #2196F3; color: white; border: none; border-radius: 5px; cursor: pointer;">🔍 Debug</button>
                </div>
            `;
        }
    }
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 DOM loaded, starting chess game initialization...');
    
    // Add delay to ensure all scripts are fully loaded before environment check
    setTimeout(() => {
        console.log('⏱️ Scripts loading delay complete, running environment check...');
        
        // Run environment check with retry logic
        if (checkEnvironment()) {
            initializeGame();
        }
        // If check fails, the retry logic in checkEnvironment() will handle it
        
    }, 1000); // Wait 1000ms for scripts to fully load
});
