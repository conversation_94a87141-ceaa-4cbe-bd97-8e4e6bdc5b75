/**
 * 3D Chess Game - Main Application
 * Entry point for the chess game application
 */

class ChessGame {
    constructor() {
        this.scene = null;
        this.chessEngine = null;
        this.interaction = null;
        this.isInitialized = false;
        
        // Game state
        this.currentPlayer = 'white';
        this.selectedPiece = null;
        this.gameOver = false;
        this.moveHistory = [];
        
        // UI elements
        this.gameStatusEl = document.getElementById('gameStatus');
        this.moveHistoryEl = document.getElementById('moveHistory');
        this.loadingScreenEl = document.getElementById('loadingScreen');
        this.newGameBtn = document.getElementById('newGameBtn');
        this.undoBtn = document.getElementById('undoBtn');
        this.resetCameraBtn = document.getElementById('resetCameraBtn');
        
        this.init();
    }

    async init() {
        try {
            console.log('Initializing 3D Chess Game...');
            
            // Initialize chess engine
            this.chessEngine = new ChessEngine();
            
            // Initialize 3D scene
            this.scene = new ChessScene('gameCanvas');
            await this.scene.init();
            
            // Initialize interaction system
            this.interaction = new ChessInteraction(this.scene, this.chessEngine, this);
            this.interaction.init();
            
            // Set up event listeners
            this.setupEventListeners();
            
            // Start the game
            this.startNewGame();
            
            // Hide loading screen
            this.hideLoadingScreen();
            
            this.isInitialized = true;
            console.log('3D Chess Game initialized successfully!');
            
        } catch (error) {
            console.error('Failed to initialize chess game:', error);
            this.showError('Failed to load the game. Please refresh the page.');
        }
    }

    setupEventListeners() {
        // New game button
        this.newGameBtn.addEventListener('click', () => {
            this.startNewGame();
        });

        // Undo button
        this.undoBtn.addEventListener('click', () => {
            this.undoLastMove();
        });

        // Reset camera button
        this.resetCameraBtn.addEventListener('click', () => {
            this.scene.resetCamera();
        });

        // Window resize
        window.addEventListener('resize', () => {
            if (this.scene) {
                this.scene.handleResize();
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (event) => {
            this.handleKeyboard(event);
        });
    }

    handleKeyboard(event) {
        switch (event.key) {
            case 'n':
            case 'N':
                if (event.ctrlKey) {
                    event.preventDefault();
                    this.startNewGame();
                }
                break;
            case 'z':
            case 'Z':
                if (event.ctrlKey) {
                    event.preventDefault();
                    this.undoLastMove();
                }
                break;
            case 'r':
            case 'R':
                if (event.ctrlKey) {
                    event.preventDefault();
                    this.scene.resetCamera();
                }
                break;
        }
    }

    startNewGame() {
        console.log('Starting new game...');
        
        // Reset game state
        this.currentPlayer = 'white';
        this.selectedPiece = null;
        this.gameOver = false;
        this.moveHistory = [];
        
        // Reset chess engine
        this.chessEngine.resetGame();
        
        // Reset 3D scene
        if (this.scene) {
            this.scene.resetBoard();
        }
        
        // Reset interaction
        if (this.interaction) {
            this.interaction.reset();
        }
        
        // Update UI
        this.updateGameStatus();
        this.updateMoveHistory();
        this.updateButtons();
        
        console.log('New game started!');
    }

    makeMove(from, to, piece) {
        if (this.gameOver) return false;
        
        try {
            // Validate move with chess engine
            const moveResult = this.chessEngine.makeMove(from, to, piece);
            
            if (moveResult.valid) {
                // Record move in history
                this.moveHistory.push({
                    from,
                    to,
                    piece,
                    captured: moveResult.captured,
                    special: moveResult.special,
                    notation: moveResult.notation,
                    timestamp: Date.now()
                });
                
                // Switch players
                this.currentPlayer = this.currentPlayer === 'white' ? 'black' : 'white';
                
                // Check for game end conditions
                this.checkGameEnd();
                
                // Update UI
                this.updateGameStatus();
                this.updateMoveHistory();
                this.updateButtons();
                
                console.log(`Move made: ${moveResult.notation}`);
                return true;
            } else {
                console.log('Invalid move attempted');
                return false;
            }
        } catch (error) {
            console.error('Error making move:', error);
            return false;
        }
    }

    undoLastMove() {
        if (this.moveHistory.length === 0 || this.gameOver) return;
        
        try {
            const lastMove = this.moveHistory.pop();
            
            // Undo move in chess engine
            this.chessEngine.undoMove(lastMove);
            
            // Switch back player
            this.currentPlayer = this.currentPlayer === 'white' ? 'black' : 'white';
            
            // Update 3D scene
            this.scene.undoMove(lastMove);
            
            // Reset game over state
            this.gameOver = false;
            
            // Update UI
            this.updateGameStatus();
            this.updateMoveHistory();
            this.updateButtons();
            
            console.log('Move undone');
        } catch (error) {
            console.error('Error undoing move:', error);
        }
    }

    checkGameEnd() {
        const gameState = this.chessEngine.getGameState();
        
        if (gameState.checkmate) {
            this.gameOver = true;
            console.log(`Checkmate! ${this.currentPlayer === 'white' ? 'Black' : 'White'} wins!`);
        } else if (gameState.stalemate) {
            this.gameOver = true;
            console.log('Stalemate! Game is a draw.');
        } else if (gameState.draw) {
            this.gameOver = true;
            console.log('Draw!');
        }
    }

    updateGameStatus() {
        const gameState = this.chessEngine.getGameState();
        let statusText = '';
        let statusClass = '';
        
        if (this.gameOver) {
            if (gameState.checkmate) {
                statusText = `Checkmate! ${this.currentPlayer === 'white' ? 'Black' : 'White'} wins!`;
                statusClass = 'checkmate-warning';
            } else if (gameState.stalemate) {
                statusText = 'Stalemate - Draw!';
                statusClass = 'check-warning';
            } else if (gameState.draw) {
                statusText = 'Draw!';
                statusClass = 'check-warning';
            }
        } else if (gameState.check) {
            statusText = `${this.currentPlayer.charAt(0).toUpperCase() + this.currentPlayer.slice(1)} in check!`;
            statusClass = 'check-warning';
        } else {
            statusText = `${this.currentPlayer.charAt(0).toUpperCase() + this.currentPlayer.slice(1)} to move`;
        }
        
        this.gameStatusEl.innerHTML = `
            <span class="status-indicator ${this.currentPlayer}"></span>
            <span class="${statusClass}">${statusText}</span>
        `;
    }

    updateMoveHistory() {
        this.moveHistoryEl.innerHTML = '';
        
        if (this.moveHistory.length === 0) {
            this.moveHistoryEl.innerHTML = '<div class="move-entry">Game started</div>';
            return;
        }
        
        this.moveHistory.forEach((move, index) => {
            const moveNumber = Math.floor(index / 2) + 1;
            const isWhiteMove = index % 2 === 0;
            
            if (isWhiteMove) {
                this.moveHistoryEl.innerHTML += `<div class="move-entry">${moveNumber}. ${move.notation}`;
            } else {
                this.moveHistoryEl.innerHTML += ` ${move.notation}</div>`;
            }
        });
        
        // Scroll to bottom
        this.moveHistoryEl.scrollTop = this.moveHistoryEl.scrollHeight;
    }

    updateButtons() {
        this.undoBtn.disabled = this.moveHistory.length === 0 || this.gameOver;
    }

    hideLoadingScreen() {
        // Fade out loading screen for smooth transition
        this.loadingScreenEl.style.transition = 'opacity 0.5s ease-out';
        this.loadingScreenEl.style.opacity = '0';

        setTimeout(() => {
            this.loadingScreenEl.style.display = 'none';
        }, 500);
    }

    showError(message) {
        this.loadingScreenEl.innerHTML = `
            <div style="text-align: center;">
                <div style="color: #ff6b6b; font-size: 24px; margin-bottom: 20px;">⚠️ Error</div>
                <div>${message}</div>
                <button class="btn" onclick="location.reload()" style="margin-top: 20px;">Reload Page</button>
            </div>
        `;
    }

    // Public API for interaction system
    getCurrentPlayer() {
        return this.currentPlayer;
    }

    isGameOver() {
        return this.gameOver;
    }

    getSelectedPiece() {
        return this.selectedPiece;
    }

    setSelectedPiece(piece) {
        this.selectedPiece = piece;
    }
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.chessGame = new ChessGame();
});
