/**
 * 3D Chess Pieces
 * Creates and manages 3D models for all chess pieces
 */

class ChessPieces {
    constructor(scene) {
        this.scene = scene;
        this.pieceGeometries = {};
        this.initGeometries();
    }

    initGeometries() {
        // Create geometries for each piece type
        this.pieceGeometries = {
            pawn: this.createPawnGeometry(),
            rook: this.createRookGeometry(),
            knight: this.createKnightGeometry(),
            bishop: this.createBishopGeometry(),
            queen: this.createQueenGeometry(),
            king: this.createKingGeometry()
        };
        
        console.log('Chess piece geometries initialized');
    }

    createPawnGeometry() {
        const group = new THREE.Group();
        
        // Base
        const baseGeometry = new THREE.CylinderGeometry(0.15, 0.2, 0.1, 16);
        const base = new THREE.Mesh(baseGeometry);
        base.position.y = 0.05;
        group.add(base);
        
        // Body
        const bodyGeometry = new THREE.CylinderGeometry(0.12, 0.15, 0.4, 16);
        const body = new THREE.Mesh(bodyGeometry);
        body.position.y = 0.3;
        group.add(body);
        
        // Head
        const headGeometry = new THREE.SphereGeometry(0.12, 16, 12);
        const head = new THREE.Mesh(headGeometry);
        head.position.y = 0.55;
        group.add(head);
        
        return group;
    }

    createRookGeometry() {
        const group = new THREE.Group();
        
        // Base
        const baseGeometry = new THREE.CylinderGeometry(0.18, 0.22, 0.12, 8);
        const base = new THREE.Mesh(baseGeometry);
        base.position.y = 0.06;
        group.add(base);
        
        // Body
        const bodyGeometry = new THREE.CylinderGeometry(0.16, 0.18, 0.5, 8);
        const body = new THREE.Mesh(bodyGeometry);
        body.position.y = 0.37;
        group.add(body);
        
        // Top
        const topGeometry = new THREE.CylinderGeometry(0.18, 0.16, 0.1, 8);
        const top = new THREE.Mesh(topGeometry);
        top.position.y = 0.67;
        group.add(top);
        
        // Crenellations
        for (let i = 0; i < 4; i++) {
            const angle = (i / 4) * Math.PI * 2;
            const x = Math.cos(angle) * 0.16;
            const z = Math.sin(angle) * 0.16;
            
            const crenGeometry = new THREE.BoxGeometry(0.06, 0.12, 0.06);
            const cren = new THREE.Mesh(crenGeometry);
            cren.position.set(x, 0.78, z);
            group.add(cren);
        }
        
        return group;
    }

    createKnightGeometry() {
        const group = new THREE.Group();
        
        // Base
        const baseGeometry = new THREE.CylinderGeometry(0.18, 0.22, 0.12, 16);
        const base = new THREE.Mesh(baseGeometry);
        base.position.y = 0.06;
        group.add(base);
        
        // Body (horse-like shape)
        const bodyGeometry = new THREE.CylinderGeometry(0.14, 0.18, 0.4, 16);
        const body = new THREE.Mesh(bodyGeometry);
        body.position.y = 0.32;
        group.add(body);
        
        // Neck
        const neckGeometry = new THREE.CylinderGeometry(0.1, 0.14, 0.25, 12);
        const neck = new THREE.Mesh(neckGeometry);
        neck.position.set(0.05, 0.6, 0);
        neck.rotation.z = Math.PI / 8;
        group.add(neck);
        
        // Head
        const headGeometry = new THREE.BoxGeometry(0.12, 0.15, 0.2);
        const head = new THREE.Mesh(headGeometry);
        head.position.set(0.12, 0.75, 0);
        group.add(head);
        
        // Ears
        const earGeometry = new THREE.ConeGeometry(0.03, 0.08, 6);
        const ear1 = new THREE.Mesh(earGeometry);
        ear1.position.set(0.08, 0.82, -0.06);
        const ear2 = new THREE.Mesh(earGeometry);
        ear2.position.set(0.08, 0.82, 0.06);
        group.add(ear1);
        group.add(ear2);
        
        return group;
    }

    createBishopGeometry() {
        const group = new THREE.Group();
        
        // Base
        const baseGeometry = new THREE.CylinderGeometry(0.18, 0.22, 0.12, 16);
        const base = new THREE.Mesh(baseGeometry);
        base.position.y = 0.06;
        group.add(base);
        
        // Body
        const bodyGeometry = new THREE.CylinderGeometry(0.12, 0.18, 0.4, 16);
        const body = new THREE.Mesh(bodyGeometry);
        body.position.y = 0.32;
        group.add(body);
        
        // Upper body
        const upperGeometry = new THREE.CylinderGeometry(0.08, 0.12, 0.25, 16);
        const upper = new THREE.Mesh(upperGeometry);
        upper.position.y = 0.645;
        group.add(upper);
        
        // Mitre (bishop's hat)
        const mitreGeometry = new THREE.ConeGeometry(0.08, 0.2, 16);
        const mitre = new THREE.Mesh(mitreGeometry);
        mitre.position.y = 0.87;
        group.add(mitre);
        
        // Cross on top
        const crossVertGeometry = new THREE.CylinderGeometry(0.01, 0.01, 0.08, 8);
        const crossVert = new THREE.Mesh(crossVertGeometry);
        crossVert.position.y = 1.01;
        group.add(crossVert);
        
        const crossHorGeometry = new THREE.CylinderGeometry(0.01, 0.01, 0.06, 8);
        const crossHor = new THREE.Mesh(crossHorGeometry);
        crossHor.position.y = 1.0;
        crossHor.rotation.z = Math.PI / 2;
        group.add(crossHor);
        
        return group;
    }

    createQueenGeometry() {
        const group = new THREE.Group();
        
        // Base
        const baseGeometry = new THREE.CylinderGeometry(0.2, 0.24, 0.12, 16);
        const base = new THREE.Mesh(baseGeometry);
        base.position.y = 0.06;
        group.add(base);
        
        // Body
        const bodyGeometry = new THREE.CylinderGeometry(0.14, 0.2, 0.45, 16);
        const body = new THREE.Mesh(bodyGeometry);
        body.position.y = 0.345;
        group.add(body);
        
        // Upper body
        const upperGeometry = new THREE.CylinderGeometry(0.12, 0.14, 0.2, 16);
        const upper = new THREE.Mesh(upperGeometry);
        upper.position.y = 0.67;
        group.add(upper);
        
        // Crown base
        const crownBaseGeometry = new THREE.CylinderGeometry(0.14, 0.12, 0.08, 16);
        const crownBase = new THREE.Mesh(crownBaseGeometry);
        crownBase.position.y = 0.81;
        group.add(crownBase);
        
        // Crown spikes
        for (let i = 0; i < 8; i++) {
            const angle = (i / 8) * Math.PI * 2;
            const x = Math.cos(angle) * 0.12;
            const z = Math.sin(angle) * 0.12;
            
            const height = i % 2 === 0 ? 0.15 : 0.1;
            const spikeGeometry = new THREE.ConeGeometry(0.02, height, 6);
            const spike = new THREE.Mesh(spikeGeometry);
            spike.position.set(x, 0.85 + height / 2, z);
            group.add(spike);
        }
        
        return group;
    }

    createKingGeometry() {
        const group = new THREE.Group();
        
        // Base
        const baseGeometry = new THREE.CylinderGeometry(0.22, 0.26, 0.12, 16);
        const base = new THREE.Mesh(baseGeometry);
        base.position.y = 0.06;
        group.add(base);
        
        // Body
        const bodyGeometry = new THREE.CylinderGeometry(0.16, 0.22, 0.5, 16);
        const body = new THREE.Mesh(bodyGeometry);
        body.position.y = 0.37;
        group.add(body);
        
        // Upper body
        const upperGeometry = new THREE.CylinderGeometry(0.14, 0.16, 0.22, 16);
        const upper = new THREE.Mesh(upperGeometry);
        upper.position.y = 0.73;
        group.add(upper);
        
        // Crown base
        const crownBaseGeometry = new THREE.CylinderGeometry(0.16, 0.14, 0.1, 16);
        const crownBase = new THREE.Mesh(crownBaseGeometry);
        crownBase.position.y = 0.89;
        group.add(crownBase);
        
        // Crown points
        for (let i = 0; i < 6; i++) {
            const angle = (i / 6) * Math.PI * 2;
            const x = Math.cos(angle) * 0.14;
            const z = Math.sin(angle) * 0.14;
            
            const pointGeometry = new THREE.ConeGeometry(0.025, 0.12, 6);
            const point = new THREE.Mesh(pointGeometry);
            point.position.set(x, 1.0, z);
            group.add(point);
        }
        
        // Cross on top
        const crossVertGeometry = new THREE.CylinderGeometry(0.015, 0.015, 0.1, 8);
        const crossVert = new THREE.Mesh(crossVertGeometry);
        crossVert.position.y = 1.15;
        group.add(crossVert);
        
        const crossHorGeometry = new THREE.CylinderGeometry(0.015, 0.015, 0.08, 8);
        const crossHor = new THREE.Mesh(crossHorGeometry);
        crossHor.position.y = 1.13;
        crossHor.rotation.z = Math.PI / 2;
        group.add(crossHor);
        
        return group;
    }

    createPiece(type, color, position) {
        if (!this.pieceGeometries[type]) {
            console.error(`Unknown piece type: ${type}`);
            return null;
        }
        
        // Clone the geometry
        const pieceGroup = this.pieceGeometries[type].clone();
        
        // Apply material to all meshes in the group
        const material = color === 'white' ? this.scene.materials.whitePiece : this.scene.materials.blackPiece;
        
        pieceGroup.traverse((child) => {
            if (child instanceof THREE.Mesh) {
                child.material = material;
                child.castShadow = true;
                child.receiveShadow = true;
            }
        });
        
        // Set position
        const { row, col } = this.scene.getPositionFromNotation(position);
        const { x, z } = this.scene.getWorldPosition(row, col);
        pieceGroup.position.set(x, 0, z);
        
        // Add metadata
        pieceGroup.userData = {
            type: 'piece',
            pieceType: type,
            color: color,
            position: position,
            originalPosition: position
        };
        
        return pieceGroup;
    }

    // Helper method to create all pieces for initial board setup
    createInitialPieces() {
        const pieces = [];
        
        // White pieces
        const whitePieces = [
            { type: 'rook', positions: ['a1', 'h1'] },
            { type: 'knight', positions: ['b1', 'g1'] },
            { type: 'bishop', positions: ['c1', 'f1'] },
            { type: 'queen', positions: ['d1'] },
            { type: 'king', positions: ['e1'] },
            { type: 'pawn', positions: ['a2', 'b2', 'c2', 'd2', 'e2', 'f2', 'g2', 'h2'] }
        ];
        
        // Black pieces
        const blackPieces = [
            { type: 'rook', positions: ['a8', 'h8'] },
            { type: 'knight', positions: ['b8', 'g8'] },
            { type: 'bishop', positions: ['c8', 'f8'] },
            { type: 'queen', positions: ['d8'] },
            { type: 'king', positions: ['e8'] },
            { type: 'pawn', positions: ['a7', 'b7', 'c7', 'd7', 'e7', 'f7', 'g7', 'h7'] }
        ];
        
        // Create white pieces
        whitePieces.forEach(pieceInfo => {
            pieceInfo.positions.forEach(position => {
                const piece = this.createPiece(pieceInfo.type, 'white', position);
                if (piece) {
                    pieces.push(piece);
                }
            });
        });
        
        // Create black pieces
        blackPieces.forEach(pieceInfo => {
            pieceInfo.positions.forEach(position => {
                const piece = this.createPiece(pieceInfo.type, 'black', position);
                if (piece) {
                    pieces.push(piece);
                }
            });
        });
        
        return pieces;
    }
}
