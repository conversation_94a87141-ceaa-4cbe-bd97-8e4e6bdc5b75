/**
 * 3D Chess Scene Manager
 * Handles Three.js scene setup, rendering, and 3D environment
 */

class ChessScene {
    constructor(canvasId) {
        this.canvasId = canvasId;
        this.canvas = null;
        this.renderer = null;
        this.scene = null;
        this.camera = null;
        this.controls = null;
        
        // Scene objects
        this.board = null;
        this.pieces = new Map(); // Map of position -> piece mesh
        this.boardSquares = []; // Array of board square meshes
        this.highlightedSquares = []; // Currently highlighted squares
        
        // Lighting
        this.ambientLight = null;
        this.directionalLight = null;
        this.pointLight = null;
        
        // Materials
        this.materials = {
            lightSquare: null,
            darkSquare: null,
            whitePiece: null,
            blackPiece: null,
            highlight: null,
            validMove: null,
            selected: null
        };
        
        // Animation
        this.animationId = null;
        this.isAnimating = false;
        
        // Constants
        this.BOARD_SIZE = 8;
        this.SQUARE_SIZE = 1;
        this.PIECE_HEIGHT = 0.8;
    }

    async init() {
        try {
            console.log('Initializing 3D scene...');
            
            // Get canvas element
            this.canvas = document.getElementById(this.canvasId);
            if (!this.canvas) {
                throw new Error(`Canvas element with id '${this.canvasId}' not found`);
            }
            
            // Initialize renderer
            this.initRenderer();
            
            // Initialize scene
            this.initScene();
            
            // Initialize camera
            this.initCamera();
            
            // Initialize controls
            this.initControls();
            
            // Initialize lighting
            this.initLighting();
            
            // Initialize materials
            this.initMaterials();
            
            // Create chess board
            this.createChessBoard();

            // Set up initial chess position
            this.setupInitialPosition();

            // Start render loop
            this.startRenderLoop();
            
            console.log('3D scene initialized successfully');
            
        } catch (error) {
            console.error('Failed to initialize 3D scene:', error);
            throw error;
        }
    }

    initRenderer() {
        this.renderer = new THREE.WebGLRenderer({
            canvas: this.canvas,
            antialias: true,
            alpha: false
        });
        
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.outputColorSpace = THREE.SRGBColorSpace;
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1.2;
        
        console.log('Renderer initialized');
    }

    initScene() {
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x2c3e50);
        this.scene.fog = new THREE.Fog(0x2c3e50, 10, 50);
        
        console.log('Scene initialized');
    }

    initCamera() {
        const aspect = window.innerWidth / window.innerHeight;
        this.camera = new THREE.PerspectiveCamera(60, aspect, 0.1, 100);
        
        // Position camera for optimal chess board viewing
        this.camera.position.set(6, 8, 6);
        this.camera.lookAt(0, 0, 0);
        
        console.log('Camera initialized');
    }

    initControls() {
        this.controls = new THREE.OrbitControls(this.camera, this.canvas);
        
        // Configure controls for chess game
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;
        this.controls.screenSpacePanning = false;
        
        // Set limits
        this.controls.minDistance = 5;
        this.controls.maxDistance = 20;
        this.controls.maxPolarAngle = Math.PI / 2.2; // Prevent going under the board
        this.controls.minPolarAngle = Math.PI / 6;   // Prevent going too high
        
        // Set target to center of board
        this.controls.target.set(0, 0, 0);
        this.controls.update();
        
        console.log('Controls initialized');
    }

    initLighting() {
        // Ambient light for overall illumination
        this.ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        this.scene.add(this.ambientLight);
        
        // Main directional light (sun-like)
        this.directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
        this.directionalLight.position.set(5, 10, 5);
        this.directionalLight.castShadow = true;
        
        // Configure shadow properties
        this.directionalLight.shadow.mapSize.width = 2048;
        this.directionalLight.shadow.mapSize.height = 2048;
        this.directionalLight.shadow.camera.near = 0.5;
        this.directionalLight.shadow.camera.far = 50;
        this.directionalLight.shadow.camera.left = -10;
        this.directionalLight.shadow.camera.right = 10;
        this.directionalLight.shadow.camera.top = 10;
        this.directionalLight.shadow.camera.bottom = -10;
        this.directionalLight.shadow.bias = -0.0001;
        
        this.scene.add(this.directionalLight);
        
        // Point light for additional illumination
        this.pointLight = new THREE.PointLight(0xffffff, 0.5, 20);
        this.pointLight.position.set(-5, 8, -5);
        this.pointLight.castShadow = true;
        this.scene.add(this.pointLight);
        
        console.log('Lighting initialized');
    }

    initMaterials() {
        // Board square materials
        this.materials.lightSquare = new THREE.MeshLambertMaterial({
            color: 0xf0d9b5,
            transparent: false
        });
        
        this.materials.darkSquare = new THREE.MeshLambertMaterial({
            color: 0xb58863,
            transparent: false
        });
        
        // Piece materials with enhanced appearance
        this.materials.whitePiece = new THREE.MeshPhongMaterial({
            color: 0xf8f8f8,
            shininess: 100,
            specular: 0x444444,
            transparent: false
        });

        this.materials.blackPiece = new THREE.MeshPhongMaterial({
            color: 0x2c2c2c,
            shininess: 100,
            specular: 0x666666,
            transparent: false
        });
        
        // Highlight materials
        this.materials.highlight = new THREE.MeshBasicMaterial({
            color: 0xffff00,
            transparent: true,
            opacity: 0.6
        });
        
        this.materials.validMove = new THREE.MeshBasicMaterial({
            color: 0x00ff00,
            transparent: true,
            opacity: 0.4
        });
        
        this.materials.selected = new THREE.MeshBasicMaterial({
            color: 0xff6b6b,
            transparent: true,
            opacity: 0.7
        });
        
        console.log('Materials initialized');
    }

    createChessBoard() {
        const boardGroup = new THREE.Group();
        this.boardSquares = [];
        
        // Create board squares
        const squareGeometry = new THREE.BoxGeometry(this.SQUARE_SIZE, 0.1, this.SQUARE_SIZE);
        
        for (let row = 0; row < this.BOARD_SIZE; row++) {
            for (let col = 0; col < this.BOARD_SIZE; col++) {
                const isLight = (row + col) % 2 === 0;
                const material = isLight ? this.materials.lightSquare : this.materials.darkSquare;
                
                const square = new THREE.Mesh(squareGeometry, material);
                
                // Position square
                const x = (col - 3.5) * this.SQUARE_SIZE;
                const z = (row - 3.5) * this.SQUARE_SIZE;
                square.position.set(x, -0.05, z);
                
                // Add metadata
                square.userData = {
                    type: 'square',
                    row: row,
                    col: col,
                    position: this.getPositionNotation(row, col)
                };
                
                square.receiveShadow = true;
                boardGroup.add(square);
                this.boardSquares.push(square);
            }
        }
        
        // Create board border
        const borderGeometry = new THREE.BoxGeometry(
            this.BOARD_SIZE * this.SQUARE_SIZE + 0.4,
            0.2,
            this.BOARD_SIZE * this.SQUARE_SIZE + 0.4
        );
        const borderMaterial = new THREE.MeshLambertMaterial({ color: 0x8b4513 });
        const border = new THREE.Mesh(borderGeometry, borderMaterial);
        border.position.y = -0.15;
        border.receiveShadow = true;
        boardGroup.add(border);
        
        this.board = boardGroup;
        this.scene.add(this.board);
        
        console.log('Chess board created');
    }

    getPositionNotation(row, col) {
        const files = 'abcdefgh';
        const ranks = '87654321';
        return files[col] + ranks[row];
    }

    getPositionFromNotation(notation) {
        const files = 'abcdefgh';
        const ranks = '87654321';
        const col = files.indexOf(notation[0]);
        const row = ranks.indexOf(notation[1]);
        return { row, col };
    }

    getWorldPosition(row, col) {
        const x = (col - 3.5) * this.SQUARE_SIZE;
        const z = (row - 3.5) * this.SQUARE_SIZE;
        return { x, z };
    }

    startRenderLoop() {
        let lastTime = 0;
        const targetFPS = 60;
        const frameInterval = 1000 / targetFPS;

        const animate = (currentTime) => {
            this.animationId = requestAnimationFrame(animate);

            // Throttle rendering to target FPS
            if (currentTime - lastTime >= frameInterval) {
                // Update controls
                this.controls.update();

                // Add subtle board rotation for visual appeal
                if (this.board) {
                    this.board.rotation.y += 0.001;
                }

                // Render scene
                this.renderer.render(this.scene, this.camera);

                lastTime = currentTime;
            }
        };

        animate(0);
        console.log('Render loop started');
    }

    handleResize() {
        const width = window.innerWidth;
        const height = window.innerHeight;
        
        // Update camera
        this.camera.aspect = width / height;
        this.camera.updateProjectionMatrix();
        
        // Update renderer
        this.renderer.setSize(width, height);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    }

    resetCamera() {
        this.camera.position.set(6, 8, 6);
        this.controls.target.set(0, 0, 0);
        this.controls.update();
    }

    resetBoard() {
        // Clear all pieces
        this.pieces.forEach(piece => {
            this.scene.remove(piece);
        });
        this.pieces.clear();
        
        // Clear highlights
        this.clearHighlights();
        
        console.log('Board reset');
    }

    clearHighlights() {
        this.highlightedSquares.forEach(highlight => {
            this.scene.remove(highlight);
        });
        this.highlightedSquares = [];
    }

    // Piece management methods
    addPiece(piece, position) {
        if (!piece) return false;

        // Remove any existing piece at this position
        this.removePieceAt(position);

        // Add piece to scene
        this.scene.add(piece);

        // Store in pieces map
        this.pieces.set(position, piece);

        // Update piece position
        piece.userData.position = position;

        return true;
    }

    removePieceAt(position) {
        const piece = this.pieces.get(position);
        if (piece) {
            this.scene.remove(piece);
            this.pieces.delete(position);
            return piece;
        }
        return null;
    }

    movePiece(fromPosition, toPosition, animate = true) {
        const piece = this.pieces.get(fromPosition);
        if (!piece) return false;

        // Remove piece from old position
        this.pieces.delete(fromPosition);

        // Remove any piece at destination
        this.removePieceAt(toPosition);

        // Update piece position
        const { row, col } = this.getPositionFromNotation(toPosition);
        const { x, z } = this.getWorldPosition(row, col);

        if (animate) {
            // Animate piece movement
            this.animatePieceMovement(piece, { x, z }, () => {
                piece.userData.position = toPosition;
                this.pieces.set(toPosition, piece);
            });
        } else {
            // Instant movement
            piece.position.set(x, 0, z);
            piece.userData.position = toPosition;
            this.pieces.set(toPosition, piece);
        }

        return true;
    }

    animatePieceMovement(piece, targetPosition, onComplete) {
        if (this.isAnimating) return;

        this.isAnimating = true;
        const startPosition = { x: piece.position.x, z: piece.position.z };
        const duration = 500; // milliseconds
        const startTime = Date.now();

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Easing function (ease-out)
            const easeOut = 1 - Math.pow(1 - progress, 3);

            // Interpolate position
            piece.position.x = startPosition.x + (targetPosition.x - startPosition.x) * easeOut;
            piece.position.z = startPosition.z + (targetPosition.z - startPosition.z) * easeOut;

            // Add slight arc to movement
            piece.position.y = Math.sin(progress * Math.PI) * 0.3;

            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                piece.position.y = 0;
                this.isAnimating = false;
                if (onComplete) onComplete();
            }
        };

        animate();
    }

    getPieceAt(position) {
        return this.pieces.get(position) || null;
    }

    highlightSquare(position, type = 'highlight') {
        const { row, col } = this.getPositionFromNotation(position);
        const { x, z } = this.getWorldPosition(row, col);

        const geometry = new THREE.PlaneGeometry(this.SQUARE_SIZE * 0.9, this.SQUARE_SIZE * 0.9);
        const material = this.materials[type] || this.materials.highlight;

        const highlight = new THREE.Mesh(geometry, material);
        highlight.position.set(x, 0.01, z);
        highlight.rotation.x = -Math.PI / 2;

        highlight.userData = {
            type: 'highlight',
            position: position
        };

        this.scene.add(highlight);
        this.highlightedSquares.push(highlight);

        return highlight;
    }

    highlightValidMoves(positions) {
        this.clearHighlights();

        positions.forEach(position => {
            this.highlightSquare(position, 'validMove');
        });
    }

    highlightSelectedPiece(position) {
        this.highlightSquare(position, 'selected');
    }

    setupInitialPosition() {
        // Clear existing pieces
        this.resetBoard();

        // Create piece factory
        const pieceFactory = new ChessPieces(this);

        // Create and position all initial pieces
        const initialPieces = pieceFactory.createInitialPieces();

        initialPieces.forEach(piece => {
            const position = piece.userData.position;
            this.addPiece(piece, position);
        });

        console.log('Initial chess position set up');
    }

    undoMove(moveData) {
        // Move piece back to original position
        this.movePiece(moveData.to, moveData.from, false);

        // Restore captured piece if any
        if (moveData.captured) {
            const pieceFactory = new ChessPieces(this);
            const restoredPiece = pieceFactory.createPiece(
                moveData.captured.type,
                moveData.captured.color,
                moveData.to
            );

            if (restoredPiece) {
                this.addPiece(restoredPiece, moveData.to);
            }
        }

        // Handle special moves (castling, en passant)
        if (moveData.special) {
            this.handleSpecialMoveUndo(moveData);
        }
    }

    handleSpecialMoveUndo(moveData) {
        switch (moveData.special) {
            case 'castling':
                // Move rook back to original position
                if (moveData.to === 'g1') {
                    this.movePiece('f1', 'h1', false);
                } else if (moveData.to === 'c1') {
                    this.movePiece('d1', 'a1', false);
                } else if (moveData.to === 'g8') {
                    this.movePiece('f8', 'h8', false);
                } else if (moveData.to === 'c8') {
                    this.movePiece('d8', 'a8', false);
                }
                break;

            case 'enPassant':
                // Restore captured pawn
                const pieceFactory = new ChessPieces(this);
                const capturedPawn = pieceFactory.createPiece('pawn', moveData.captured.color, moveData.captured.position);
                if (capturedPawn) {
                    this.addPiece(capturedPawn, moveData.captured.position);
                }
                break;
        }
    }

    dispose() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }

        if (this.renderer) {
            this.renderer.dispose();
        }

        console.log('Scene disposed');
    }
}
