const { chromium } = require('playwright');

async function testActualGameplay() {
    console.log('🎮 Testing actual game loading and interaction...');
    
    const browser = await chromium.launch({ headless: false });
    const page = await browser.newPage();
    
    // Capture all console messages
    page.on('console', msg => {
        const type = msg.type();
        const text = msg.text();
        const icon = type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
        console.log(`${icon} [BROWSER] ${text}`);
    });
    
    // Capture errors
    page.on('pageerror', error => {
        console.error(`💥 [PAGE ERROR] ${error.message}`);
    });
    
    try {
        console.log('🌐 Loading game page...');
        await page.goto('http://localhost:8000/');
        
        console.log('⏳ Waiting for game to fully initialize...');
        
        // Wait for loading screen to disappear (or timeout after 20 seconds)
        try {
            await page.waitForSelector('#loadingScreen', { state: 'hidden', timeout: 20000 });
            console.log('✅ Loading screen disappeared - game initialized!');
        } catch (error) {
            console.log('❌ Loading screen timeout - checking what\'s visible...');
            
            // Take screenshot to see current state
            await page.screenshot({ path: 'loading-failure.png', fullPage: true });
            console.log('📸 Screenshot saved as loading-failure.png');
            
            // Check what's currently visible
            const loadingVisible = await page.isVisible('#loadingScreen');
            const canvasVisible = await page.isVisible('#gameCanvas');
            const errorVisible = await page.locator('div:has-text("Error")').isVisible().catch(() => false);
            
            console.log(`📊 Current state:`);
            console.log(`   Loading screen visible: ${loadingVisible}`);
            console.log(`   Canvas visible: ${canvasVisible}`);
            console.log(`   Error message visible: ${errorVisible}`);
            
            // Get loading screen content to see what error is showing
            if (loadingVisible) {
                const loadingContent = await page.textContent('#loadingScreen');
                console.log(`📝 Loading screen content: ${loadingContent}`);
            }
        }
        
        // Check if game is actually functional
        console.log('🧪 Testing game functionality...');
        
        const gameState = await page.evaluate(() => {
            return {
                chessGameExists: typeof window.chessGame !== 'undefined',
                chessGameInitialized: window.chessGame && window.chessGame.isInitialized,
                canvasVisible: document.getElementById('gameCanvas') && document.getElementById('gameCanvas').offsetWidth > 0,
                sceneExists: window.chessGame && window.chessGame.scene,
                piecesCount: window.chessGame && window.chessGame.scene && window.chessGame.scene.pieces ? window.chessGame.scene.pieces.size : 0,
                currentPlayer: window.chessGame && window.chessGame.currentPlayer,
                gameStatus: document.getElementById('gameStatus') ? document.getElementById('gameStatus').textContent : 'No status',
                moveHistory: document.getElementById('moveHistory') ? document.getElementById('moveHistory').textContent : 'No history'
            };
        });
        
        console.log('\n📊 Game State Report:');
        Object.entries(gameState).forEach(([key, value]) => {
            const icon = (key.includes('Exists') || key.includes('Initialized') || key.includes('Visible')) ? 
                         (value ? '✅' : '❌') : 'ℹ️';
            console.log(`${icon} ${key}: ${value}`);
        });
        
        // If game seems loaded, try a click interaction
        if (gameState.chessGameInitialized && gameState.canvasVisible) {
            console.log('\n🖱️ Testing click interaction...');
            
            try {
                // Click on the center of the canvas
                await page.click('#gameCanvas', { position: { x: 400, y: 300 } });
                console.log('✅ Canvas click successful');
                
                // Wait a moment and check if anything changed
                await page.waitForTimeout(1000);
                
                const afterClick = await page.evaluate(() => {
                    return {
                        selectedPiece: window.chessGame && window.chessGame.selectedPiece ? 'piece selected' : 'no selection',
                        gameStatus: document.getElementById('gameStatus').textContent
                    };
                });
                
                console.log('📊 After click:');
                Object.entries(afterClick).forEach(([key, value]) => {
                    console.log(`   ${key}: ${value}`);
                });
                
            } catch (error) {
                console.error(`❌ Click interaction failed: ${error.message}`);
            }
        }
        
        // Keep browser open for 10 seconds for manual inspection
        console.log('\n👀 Keeping browser open for 10 seconds for manual inspection...');
        await page.waitForTimeout(10000);
        
    } catch (error) {
        console.error(`💥 Test failed: ${error.message}`);
        await page.screenshot({ path: 'test-failure.png', fullPage: true });
        console.log('📸 Error screenshot saved as test-failure.png');
    } finally {
        await browser.close();
    }
}

testActualGameplay().catch(console.error);