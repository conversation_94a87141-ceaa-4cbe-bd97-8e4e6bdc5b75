{"name": "3d-chess-game", "version": "1.0.0", "description": "A fully functional 3D chess game built with Three.js", "main": "index.html", "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:ui": "playwright test --ui", "serve": "python -m http.server 8000", "serve:node": "npx http-server -p 8000"}, "keywords": ["chess", "3d", "threejs", "webgl", "game"], "author": "AI Assistant", "license": "MIT", "devDependencies": {"@playwright/test": "^1.55.0"}}