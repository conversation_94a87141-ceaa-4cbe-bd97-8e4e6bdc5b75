const { chromium } = require('playwright');

async function quickFixTest() {
    console.log('🔧 Quick fix test - checking loading screen behavior...');
    
    const browser = await chromium.launch({ headless: false });
    const page = await browser.newPage();
    
    try {
        console.log('🌐 Loading page...');
        await page.goto('http://localhost:8000/');
        
        // Wait a moment for initial load
        await page.waitForTimeout(2000);
        
        console.log('📊 Checking loading screen state every 2 seconds...');
        
        for (let i = 0; i < 10; i++) {
            const loadingState = await page.evaluate(() => {
                const loadingEl = document.getElementById('loadingScreen');
                if (!loadingEl) return 'NOT_FOUND';
                
                return {
                    display: loadingEl.style.display,
                    opacity: loadingEl.style.opacity,
                    visible: loadingEl.offsetWidth > 0 && loadingEl.offsetHeight > 0,
                    innerHTML: loadingEl.innerHTML.substring(0, 100) + '...'
                };
            });
            
            console.log(`⏰ Check ${i + 1}: Loading screen state:`, loadingState);
            
            if (loadingState === 'NOT_FOUND' || (loadingState.display === 'none' && !loadingState.visible)) {
                console.log('✅ Loading screen is properly hidden!');
                break;
            }
            
            await page.waitForTimeout(2000);
        }
        
        // Final check of game state
        console.log('\n🎮 Final game state check:');
        const finalState = await page.evaluate(() => {
            return {
                gameLoaded: window.chessGame && window.chessGame.isInitialized,
                canvasVisible: document.getElementById('gameCanvas') && document.getElementById('gameCanvas').offsetWidth > 0,
                loadingScreenHidden: !document.getElementById('loadingScreen') || document.getElementById('loadingScreen').style.display === 'none',
                gameStatus: document.getElementById('gameStatus') ? document.getElementById('gameStatus').textContent.trim() : 'NO STATUS'
            };
        });
        
        console.log('📈 Final State:');
        Object.entries(finalState).forEach(([key, value]) => {
            const icon = value === true ? '✅' : value === false ? '❌' : 'ℹ️';
            console.log(`${icon} ${key}: ${value}`);
        });
        
        // Test if we can click to select a piece
        if (finalState.gameLoaded && finalState.canvasVisible) {
            console.log('\n🖱️ Testing piece selection...');
            
            // Try clicking on different areas of the board
            const testClicks = [
                { x: 200, y: 400, name: 'left side (white pieces)' },
                { x: 400, y: 400, name: 'center' },
                { x: 600, y: 400, name: 'right side' },
                { x: 400, y: 200, name: 'top (black pieces)' },
                { x: 400, y: 600, name: 'bottom (white pieces)' }
            ];
            
            for (const click of testClicks) {
                console.log(`🎯 Clicking ${click.name} at (${click.x}, ${click.y})...`);
                await page.click('#gameCanvas', { position: { x: click.x, y: click.y } });
                await page.waitForTimeout(500);
                
                const clickResult = await page.evaluate(() => {
                    return {
                        selectedPiece: window.chessGame && window.chessGame.selectedPiece ? 'PIECE_SELECTED' : 'NO_SELECTION',
                        gameStatus: document.getElementById('gameStatus').textContent.trim()
                    };
                });
                
                console.log(`   Result: ${clickResult.selectedPiece}`);
                
                if (clickResult.selectedPiece === 'PIECE_SELECTED') {
                    console.log('✅ Successfully selected a piece!');
                    break;
                }
            }
        }
        
        // Keep browser open for manual inspection
        console.log('\n👁️ Browser will stay open for 15 seconds for manual testing...');
        console.log('   Try clicking on chess pieces to select them and move them!');
        await page.waitForTimeout(15000);
        
    } catch (error) {
        console.error(`💥 Test failed: ${error.message}`);
    } finally {
        await browser.close();
    }
}

quickFixTest().catch(console.error);