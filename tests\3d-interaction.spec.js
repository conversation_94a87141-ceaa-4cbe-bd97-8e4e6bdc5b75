const { test, expect } = require('@playwright/test');

test.describe('3D Scene and Interaction', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.waitForSelector('#gameCanvas', { timeout: 10000 });
    await page.waitForSelector('#loadingScreen', { state: 'hidden', timeout: 15000 });
    await page.waitForTimeout(3000); // Extra time for 3D scene initialization
  });

  test('should initialize 3D scene properly', async ({ page }) => {
    const sceneState = await page.evaluate(() => {
      return {
        hasScene: window.chessGame && window.chessGame.scene !== null,
        hasRenderer: window.chessGame && window.chessGame.scene && window.chessGame.scene.renderer !== null,
        hasCamera: window.chessGame && window.chessGame.scene && window.chessGame.scene.camera !== null,
        hasControls: window.chessGame && window.chessGame.scene && window.chessGame.scene.controls !== null,
        piecesCount: window.chessGame && window.chessGame.scene ? window.chessGame.scene.pieces.size : 0,
        boardSquaresCount: window.chessGame && window.chessGame.scene ? window.chessGame.scene.boardSquares.length : 0
      };
    });

    expect(sceneState.hasScene).toBe(true);
    expect(sceneState.hasRenderer).toBe(true);
    expect(sceneState.hasCamera).toBe(true);
    expect(sceneState.hasControls).toBe(true);
    expect(sceneState.piecesCount).toBe(32); // 32 chess pieces
    expect(sceneState.boardSquaresCount).toBe(64); // 64 board squares
  });

  test('should handle canvas mouse interactions', async ({ page }) => {
    const canvas = page.locator('#gameCanvas');
    
    // Test mouse move
    await canvas.hover({ position: { x: 400, y: 300 } });
    
    // Test click
    await canvas.click({ position: { x: 400, y: 300 } });
    
    // Verify no errors occurred
    const hasErrors = await page.evaluate(() => {
      return window.chessGame && window.chessGame.scene && !window.chessGame.scene.renderer;
    });
    
    expect(hasErrors).toBe(false);
  });

  test('should handle touch interactions', async ({ page }) => {
    const canvas = page.locator('#gameCanvas');
    
    // Simulate touch events
    await canvas.dispatchEvent('touchstart', {
      touches: [{ clientX: 400, clientY: 300 }],
      targetTouches: [{ clientX: 400, clientY: 300 }],
      changedTouches: [{ clientX: 400, clientY: 300 }]
    });
    
    await page.waitForTimeout(100);
    
    await canvas.dispatchEvent('touchend', {
      touches: [],
      targetTouches: [],
      changedTouches: [{ clientX: 400, clientY: 300 }]
    });
    
    // Verify scene is still functional
    const sceneActive = await page.evaluate(() => {
      return window.chessGame && window.chessGame.scene && window.chessGame.scene.renderer !== null;
    });
    
    expect(sceneActive).toBe(true);
  });

  test('should handle camera controls', async ({ page }) => {
    const initialCameraState = await page.evaluate(() => {
      const camera = window.chessGame.scene.camera;
      return {
        x: camera.position.x,
        y: camera.position.y,
        z: camera.position.z
      };
    });

    // Reset camera
    const resetCameraBtn = page.locator('#resetCameraBtn');
    await resetCameraBtn.click();
    
    await page.waitForTimeout(500);
    
    const resetCameraState = await page.evaluate(() => {
      const camera = window.chessGame.scene.camera;
      return {
        x: camera.position.x,
        y: camera.position.y,
        z: camera.position.z
      };
    });

    // Camera should be at expected reset position
    expect(resetCameraState.x).toBeCloseTo(6, 1);
    expect(resetCameraState.y).toBeCloseTo(8, 1);
    expect(resetCameraState.z).toBeCloseTo(6, 1);
  });

  test('should handle window resize', async ({ page }) => {
    // Get initial canvas size
    const initialSize = await page.evaluate(() => {
      const canvas = window.chessGame.scene.canvas;
      return {
        width: canvas.width,
        height: canvas.height
      };
    });

    // Resize window
    await page.setViewportSize({ width: 800, height: 600 });
    
    // Trigger resize event
    await page.evaluate(() => {
      window.dispatchEvent(new Event('resize'));
    });
    
    await page.waitForTimeout(500);
    
    // Check that canvas was resized
    const newSize = await page.evaluate(() => {
      const canvas = window.chessGame.scene.canvas;
      return {
        width: canvas.width,
        height: canvas.height
      };
    });

    // Canvas should have been resized
    expect(newSize.width).not.toBe(initialSize.width);
    expect(newSize.height).not.toBe(initialSize.height);
  });

  test('should render without WebGL errors', async ({ page }) => {
    // Check for WebGL context
    const webglSupport = await page.evaluate(() => {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      return gl !== null;
    });

    if (webglSupport) {
      // Check renderer state
      const rendererState = await page.evaluate(() => {
        const renderer = window.chessGame.scene.renderer;
        const gl = renderer.getContext();
        return {
          hasContext: gl !== null,
          contextLost: gl.isContextLost(),
          rendererInfo: renderer.info
        };
      });

      expect(rendererState.hasContext).toBe(true);
      expect(rendererState.contextLost).toBe(false);
    }
  });

  test('should handle piece highlighting', async ({ page }) => {
    // Simulate piece selection through JavaScript
    const highlightTest = await page.evaluate(() => {
      const scene = window.chessGame.scene;
      
      // Clear any existing highlights
      scene.clearHighlights();
      const initialHighlights = scene.highlightedSquares.length;
      
      // Highlight a square
      scene.highlightSquare('e4', 'selected');
      const afterHighlight = scene.highlightedSquares.length;
      
      // Highlight valid moves
      scene.highlightValidMoves(['e3', 'e4', 'e5']);
      const afterValidMoves = scene.highlightedSquares.length;
      
      // Clear highlights
      scene.clearHighlights();
      const afterClear = scene.highlightedSquares.length;
      
      return {
        initialHighlights,
        afterHighlight,
        afterValidMoves,
        afterClear
      };
    });

    expect(highlightTest.initialHighlights).toBe(0);
    expect(highlightTest.afterHighlight).toBe(1);
    expect(highlightTest.afterValidMoves).toBe(4); // 1 selected + 3 valid moves
    expect(highlightTest.afterClear).toBe(0);
  });

  test('should handle piece movement animation', async ({ page }) => {
    const animationTest = await page.evaluate(async () => {
      const scene = window.chessGame.scene;
      
      // Get a piece
      const piece = scene.getPieceAt('e2');
      if (!piece) return { error: 'No piece found' };
      
      const initialPosition = {
        x: piece.position.x,
        y: piece.position.y,
        z: piece.position.z
      };
      
      // Move piece (this should trigger animation)
      scene.movePiece('e2', 'e4', true);
      
      // Wait a bit for animation to start
      await new Promise(resolve => setTimeout(resolve, 100));
      
      const duringAnimation = {
        x: piece.position.x,
        y: piece.position.y,
        z: piece.position.z
      };
      
      // Wait for animation to complete
      await new Promise(resolve => setTimeout(resolve, 600));
      
      const finalPosition = {
        x: piece.position.x,
        y: piece.position.y,
        z: piece.position.z
      };
      
      return {
        initialPosition,
        duringAnimation,
        finalPosition
      };
    });

    if (!animationTest.error) {
      // Position should change during animation
      expect(animationTest.finalPosition.z).not.toBe(animationTest.initialPosition.z);
      
      // Y should return to 0 after animation
      expect(animationTest.finalPosition.y).toBeCloseTo(0, 1);
    }
  });

  test('should handle raycasting correctly', async ({ page }) => {
    const raycastTest = await page.evaluate(() => {
      const interaction = window.chessGame.interaction;
      
      if (!interaction) return { error: 'No interaction system' };
      
      // Set mouse position
      interaction.mouse.x = 0;
      interaction.mouse.y = 0;
      
      // Test raycasting
      const intersectedObject = interaction.getIntersectedObject();
      
      return {
        hasRaycaster: interaction.raycaster !== null,
        intersectedObject: intersectedObject ? {
          type: intersectedObject.userData.type,
          position: intersectedObject.userData.position
        } : null
      };
    });

    if (!raycastTest.error) {
      expect(raycastTest.hasRaycaster).toBe(true);
      // intersectedObject might be null depending on mouse position, which is fine
    }
  });

  test('should maintain performance during interactions', async ({ page }) => {
    // Test rapid interactions
    const canvas = page.locator('#gameCanvas');
    
    const startTime = Date.now();
    
    // Perform multiple rapid interactions
    for (let i = 0; i < 10; i++) {
      await canvas.click({ 
        position: { 
          x: 300 + (i % 3) * 50, 
          y: 300 + (i % 3) * 50 
        } 
      });
    }
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // Should complete within reasonable time (less than 5 seconds)
    expect(duration).toBeLessThan(5000);
    
    // Scene should still be functional
    const sceneActive = await page.evaluate(() => {
      return window.chessGame && 
             window.chessGame.scene && 
             window.chessGame.scene.renderer !== null &&
             !window.chessGame.scene.isAnimating;
    });
    
    expect(sceneActive).toBe(true);
  });

  test('should handle edge case interactions', async ({ page }) => {
    const canvas = page.locator('#gameCanvas');
    
    // Click outside canvas bounds (should not cause errors)
    await canvas.click({ position: { x: -10, y: -10 } });
    await canvas.click({ position: { x: 2000, y: 2000 } });
    
    // Rapid mouse movements
    for (let i = 0; i < 5; i++) {
      await canvas.hover({ position: { x: i * 100, y: i * 100 } });
    }
    
    // Verify scene is still stable
    const sceneStable = await page.evaluate(() => {
      return window.chessGame && 
             window.chessGame.scene && 
             window.chessGame.scene.renderer !== null;
    });
    
    expect(sceneStable).toBe(true);
  });
});
