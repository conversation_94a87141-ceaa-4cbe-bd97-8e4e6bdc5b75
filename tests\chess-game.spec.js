const { test, expect } = require('@playwright/test');

test.describe('3D Chess Game', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    
    // Wait for the game to load
    await page.waitForSelector('#gameCanvas', { timeout: 10000 });
    
    // Wait for loading screen to disappear
    await page.waitForSelector('#loadingScreen', { state: 'hidden', timeout: 15000 });
    
    // Wait a bit more for Three.js to fully initialize
    await page.waitForTimeout(2000);
  });

  test('should load the game successfully', async ({ page }) => {
    // Check that the canvas is visible
    const canvas = page.locator('#gameCanvas');
    await expect(canvas).toBeVisible();
    
    // Check that the UI is present
    const gameUI = page.locator('#gameUI');
    await expect(gameUI).toBeVisible();
    
    // Check that the game status shows white to move
    const gameStatus = page.locator('#gameStatus');
    await expect(gameStatus).toContainText('White to move');
    
    // Check that controls are present
    const newGameBtn = page.locator('#newGameBtn');
    await expect(newGameBtn).toBeVisible();
    
    const undoBtn = page.locator('#undoBtn');
    await expect(undoBtn).toBeVisible();
    await expect(undoBtn).toBeDisabled();
    
    const resetCameraBtn = page.locator('#resetCameraBtn');
    await expect(resetCameraBtn).toBeVisible();
  });

  test('should display move history', async ({ page }) => {
    const moveHistory = page.locator('#moveHistory');
    await expect(moveHistory).toBeVisible();
    await expect(moveHistory).toContainText('Game started');
  });

  test('should handle new game button', async ({ page }) => {
    const newGameBtn = page.locator('#newGameBtn');
    await newGameBtn.click();
    
    // Check that the game status still shows white to move
    const gameStatus = page.locator('#gameStatus');
    await expect(gameStatus).toContainText('White to move');
    
    // Check that move history is reset
    const moveHistory = page.locator('#moveHistory');
    await expect(moveHistory).toContainText('Game started');
  });

  test('should handle camera reset button', async ({ page }) => {
    const resetCameraBtn = page.locator('#resetCameraBtn');
    await resetCameraBtn.click();
    
    // Camera reset should not affect game state
    const gameStatus = page.locator('#gameStatus');
    await expect(gameStatus).toContainText('White to move');
  });

  test('should be responsive on mobile viewport', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Check that UI is still visible and properly sized
    const gameUI = page.locator('#gameUI');
    await expect(gameUI).toBeVisible();
    
    const canvas = page.locator('#gameCanvas');
    await expect(canvas).toBeVisible();
    
    // Check that buttons are still accessible
    const newGameBtn = page.locator('#newGameBtn');
    await expect(newGameBtn).toBeVisible();
  });

  test('should handle keyboard shortcuts', async ({ page }) => {
    // Test Ctrl+N for new game
    await page.keyboard.press('Control+n');
    
    const gameStatus = page.locator('#gameStatus');
    await expect(gameStatus).toContainText('White to move');
    
    // Test Ctrl+R for camera reset
    await page.keyboard.press('Control+r');
    
    // Should not affect game state
    await expect(gameStatus).toContainText('White to move');
  });

  test('should handle window resize', async ({ page }) => {
    // Initial size
    await page.setViewportSize({ width: 1200, height: 800 });
    
    const canvas = page.locator('#gameCanvas');
    await expect(canvas).toBeVisible();
    
    // Resize window
    await page.setViewportSize({ width: 800, height: 600 });
    
    // Canvas should still be visible and properly sized
    await expect(canvas).toBeVisible();
    
    // Game should still be functional
    const gameStatus = page.locator('#gameStatus');
    await expect(gameStatus).toContainText('White to move');
  });

  test('should show proper cursor states', async ({ page }) => {
    const canvas = page.locator('#gameCanvas');
    
    // Initially should have default cursor
    const cursorStyle = await canvas.evaluate(el => getComputedStyle(el).cursor);
    expect(['default', 'auto']).toContain(cursorStyle);
  });

  test('should handle canvas interactions', async ({ page }) => {
    const canvas = page.locator('#gameCanvas');
    
    // Click on canvas (should not cause errors)
    await canvas.click({ position: { x: 400, y: 300 } });
    
    // Game should still be functional
    const gameStatus = page.locator('#gameStatus');
    await expect(gameStatus).toContainText('White to move');
  });

  test('should maintain game state consistency', async ({ page }) => {
    // Check initial state
    const gameStatus = page.locator('#gameStatus');
    await expect(gameStatus).toContainText('White to move');
    
    const undoBtn = page.locator('#undoBtn');
    await expect(undoBtn).toBeDisabled();
    
    // Click new game multiple times
    const newGameBtn = page.locator('#newGameBtn');
    await newGameBtn.click();
    await newGameBtn.click();
    await newGameBtn.click();
    
    // State should remain consistent
    await expect(gameStatus).toContainText('White to move');
    await expect(undoBtn).toBeDisabled();
    
    const moveHistory = page.locator('#moveHistory');
    await expect(moveHistory).toContainText('Game started');
  });

  test('should handle rapid interactions gracefully', async ({ page }) => {
    const canvas = page.locator('#gameCanvas');
    const newGameBtn = page.locator('#newGameBtn');
    
    // Rapid clicks on canvas
    for (let i = 0; i < 5; i++) {
      await canvas.click({ position: { x: 200 + i * 50, y: 200 + i * 30 } });
    }
    
    // Rapid new game clicks
    for (let i = 0; i < 3; i++) {
      await newGameBtn.click();
    }
    
    // Game should still be functional
    const gameStatus = page.locator('#gameStatus');
    await expect(gameStatus).toContainText('White to move');
  });

  test('should not have console errors', async ({ page }) => {
    const consoleErrors = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });
    
    // Interact with the game
    const newGameBtn = page.locator('#newGameBtn');
    await newGameBtn.click();
    
    const canvas = page.locator('#gameCanvas');
    await canvas.click({ position: { x: 400, y: 300 } });
    
    // Wait a bit for any async errors
    await page.waitForTimeout(1000);
    
    // Check for critical errors (ignore minor warnings)
    const criticalErrors = consoleErrors.filter(error => 
      !error.includes('Warning') && 
      !error.includes('DevTools') &&
      !error.includes('favicon')
    );
    
    expect(criticalErrors).toHaveLength(0);
  });
});
