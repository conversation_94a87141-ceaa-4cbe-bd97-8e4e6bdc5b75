const { test, expect } = require('@playwright/test');

test.describe('Chess Rules and Logic', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.waitForSelector('#gameCanvas', { timeout: 10000 });
    await page.waitForSelector('#loadingScreen', { state: 'hidden', timeout: 15000 });
    await page.waitForTimeout(2000);
  });

  test('should validate chess engine initialization', async ({ page }) => {
    // Test that the chess engine is properly initialized
    const engineState = await page.evaluate(() => {
      return {
        hasChessGame: typeof window.chessGame !== 'undefined',
        hasChessEngine: window.chessGame && typeof window.chessGame.chessEngine !== 'undefined',
        currentPlayer: window.chessGame && window.chessGame.getCurrentPlayer(),
        isGameOver: window.chessGame && window.chessGame.isGameOver()
      };
    });

    expect(engineState.hasChessGame).toBe(true);
    expect(engineState.hasChessEngine).toBe(true);
    expect(engineState.currentPlayer).toBe('white');
    expect(engineState.isGameOver).toBe(false);
  });

  test('should have correct initial board setup', async ({ page }) => {
    const boardState = await page.evaluate(() => {
      const engine = window.chessGame.chessEngine;
      const pieces = {};
      
      // Check key pieces
      pieces.whiteKing = engine.getPieceAt('e1');
      pieces.blackKing = engine.getPieceAt('e8');
      pieces.whiteQueen = engine.getPieceAt('d1');
      pieces.blackQueen = engine.getPieceAt('d8');
      pieces.whitePawn = engine.getPieceAt('e2');
      pieces.blackPawn = engine.getPieceAt('e7');
      
      return pieces;
    });

    expect(boardState.whiteKing).toEqual({ type: 'k', color: 'white', hasMoved: false });
    expect(boardState.blackKing).toEqual({ type: 'k', color: 'black', hasMoved: false });
    expect(boardState.whiteQueen).toEqual({ type: 'q', color: 'white', hasMoved: false });
    expect(boardState.blackQueen).toEqual({ type: 'q', color: 'black', hasMoved: false });
    expect(boardState.whitePawn).toEqual({ type: 'p', color: 'white', hasMoved: false });
    expect(boardState.blackPawn).toEqual({ type: 'p', color: 'black', hasMoved: false });
  });

  test('should validate pawn movement rules', async ({ page }) => {
    const pawnMoves = await page.evaluate(() => {
      const engine = window.chessGame.chessEngine;
      return {
        e2Moves: engine.getValidMoves('e2'),
        e7Moves: engine.getValidMoves('e7'),
        d2Moves: engine.getValidMoves('d2')
      };
    });

    // White pawn on e2 should be able to move to e3 and e4
    expect(pawnMoves.e2Moves).toContain('e3');
    expect(pawnMoves.e2Moves).toContain('e4');
    expect(pawnMoves.e2Moves).toHaveLength(2);

    // Black pawn should not be movable on white's turn
    expect(pawnMoves.e7Moves).toHaveLength(0);

    // Another white pawn should also have two moves
    expect(pawnMoves.d2Moves).toContain('d3');
    expect(pawnMoves.d2Moves).toContain('d4');
  });

  test('should validate knight movement rules', async ({ page }) => {
    const knightMoves = await page.evaluate(() => {
      const engine = window.chessGame.chessEngine;
      return {
        b1Moves: engine.getValidMoves('b1'),
        g1Moves: engine.getValidMoves('g1')
      };
    });

    // White knight on b1 should be able to move to a3 and c3
    expect(knightMoves.b1Moves).toContain('a3');
    expect(knightMoves.b1Moves).toContain('c3');
    expect(knightMoves.b1Moves).toHaveLength(2);

    // White knight on g1 should be able to move to f3 and h3
    expect(knightMoves.g1Moves).toContain('f3');
    expect(knightMoves.g1Moves).toContain('h3');
    expect(knightMoves.g1Moves).toHaveLength(2);
  });

  test('should validate piece blocking', async ({ page }) => {
    const blockedMoves = await page.evaluate(() => {
      const engine = window.chessGame.chessEngine;
      return {
        rookMoves: engine.getValidMoves('a1'),
        bishopMoves: engine.getValidMoves('c1'),
        queenMoves: engine.getValidMoves('d1')
      };
    });

    // Rook should be blocked by pawn
    expect(blockedMoves.rookMoves).toHaveLength(0);

    // Bishop should be blocked by pawn
    expect(blockedMoves.bishopMoves).toHaveLength(0);

    // Queen should be blocked by pawns
    expect(blockedMoves.queenMoves).toHaveLength(0);
  });

  test('should handle invalid moves', async ({ page }) => {
    const invalidMoveResult = await page.evaluate(() => {
      const engine = window.chessGame.chessEngine;
      
      // Try to move a piece that doesn't exist
      const nopiece = engine.makeMove('e4', 'e5');
      
      // Try to move opponent's piece
      const opponentPiece = engine.makeMove('e7', 'e6');
      
      // Try to move to invalid position
      const invalidPos = engine.makeMove('e2', 'e9');
      
      return {
        nopiece: nopiece.valid,
        opponentPiece: opponentPiece.valid,
        invalidPos: invalidPos.valid
      };
    });

    expect(invalidMoveResult.nopiece).toBe(false);
    expect(invalidMoveResult.opponentPiece).toBe(false);
    expect(invalidMoveResult.invalidPos).toBe(false);
  });

  test('should handle valid moves and turn switching', async ({ page }) => {
    const moveResult = await page.evaluate(() => {
      const engine = window.chessGame.chessEngine;
      
      // Make a valid move
      const move1 = engine.makeMove('e2', 'e4');
      const playerAfterMove1 = engine.currentPlayer;
      
      // Make another valid move
      const move2 = engine.makeMove('e7', 'e5');
      const playerAfterMove2 = engine.currentPlayer;
      
      return {
        move1Valid: move1.valid,
        move2Valid: move2.valid,
        playerAfterMove1,
        playerAfterMove2,
        move1Notation: move1.notation,
        move2Notation: move2.notation
      };
    });

    expect(moveResult.move1Valid).toBe(true);
    expect(moveResult.move2Valid).toBe(true);
    expect(moveResult.playerAfterMove1).toBe('black');
    expect(moveResult.playerAfterMove2).toBe('white');
    expect(moveResult.move1Notation).toBe('e4');
    expect(moveResult.move2Notation).toBe('e5');
  });

  test('should handle captures', async ({ page }) => {
    const captureTest = await page.evaluate(() => {
      const engine = window.chessGame.chessEngine;
      
      // Set up a capture scenario
      engine.makeMove('e2', 'e4');
      engine.makeMove('d7', 'd5');
      
      // Capture with pawn
      const captureMove = engine.makeMove('e4', 'd5');
      
      // Check if piece was captured
      const capturedSquare = engine.getPieceAt('d5');
      const emptySquare = engine.getPieceAt('e4');
      
      return {
        captureValid: captureMove.valid,
        capturedPiece: captureMove.captured,
        capturedSquareOccupied: capturedSquare !== null,
        capturedSquareColor: capturedSquare ? capturedSquare.color : null,
        emptySquareEmpty: emptySquare === null,
        notation: captureMove.notation
      };
    });

    expect(captureTest.captureValid).toBe(true);
    expect(captureTest.capturedPiece).toBeTruthy();
    expect(captureTest.capturedSquareOccupied).toBe(true);
    expect(captureTest.capturedSquareColor).toBe('white');
    expect(captureTest.emptySquareEmpty).toBe(true);
    expect(captureTest.notation).toBe('exd5');
  });

  test('should detect check conditions', async ({ page }) => {
    const checkTest = await page.evaluate(() => {
      const engine = window.chessGame.chessEngine;
      
      // Set up a check scenario (simplified)
      // This is a basic test - in a real game, we'd need a more complex setup
      engine.makeMove('e2', 'e4');
      engine.makeMove('f7', 'f6');
      engine.makeMove('d1', 'h5'); // Queen attacks king
      
      const gameState = engine.getGameState();
      
      return {
        isCheck: gameState.check,
        isCheckmate: gameState.checkmate,
        currentPlayer: engine.currentPlayer
      };
    });

    expect(checkTest.isCheck).toBe(true);
    expect(checkTest.currentPlayer).toBe('black');
  });

  test('should validate castling rights', async ({ page }) => {
    const castlingTest = await page.evaluate(() => {
      const engine = window.chessGame.chessEngine;
      
      // Initial castling rights
      const initialRights = { ...engine.castlingRights };
      
      // Move king
      engine.makeMove('e2', 'e4');
      engine.makeMove('e7', 'e5');
      engine.makeMove('g1', 'f3');
      engine.makeMove('g8', 'f6');
      engine.makeMove('f1', 'e2');
      engine.makeMove('f8', 'e7');
      
      // Check if castling is possible
      const kingMoves = engine.getValidMoves('e1');
      const canCastleKingside = kingMoves.includes('g1');
      
      return {
        initialRights,
        canCastleKingside,
        kingMoves
      };
    });

    expect(castlingTest.initialRights.white.kingside).toBe(true);
    expect(castlingTest.initialRights.white.queenside).toBe(true);
    expect(castlingTest.canCastleKingside).toBe(true);
  });

  test('should handle game reset', async ({ page }) => {
    const resetTest = await page.evaluate(() => {
      const engine = window.chessGame.chessEngine;
      
      // Make some moves
      engine.makeMove('e2', 'e4');
      engine.makeMove('e7', 'e5');
      
      const beforeReset = {
        currentPlayer: engine.currentPlayer,
        moveHistory: engine.moveHistory.length,
        e4Piece: engine.getPieceAt('e4'),
        e2Piece: engine.getPieceAt('e2')
      };
      
      // Reset game
      engine.resetGame();
      
      const afterReset = {
        currentPlayer: engine.currentPlayer,
        moveHistory: engine.moveHistory.length,
        e4Piece: engine.getPieceAt('e4'),
        e2Piece: engine.getPieceAt('e2')
      };
      
      return { beforeReset, afterReset };
    });

    expect(resetTest.beforeReset.currentPlayer).toBe('white');
    expect(resetTest.beforeReset.moveHistory).toBe(2);
    expect(resetTest.beforeReset.e4Piece).toBeTruthy();
    expect(resetTest.beforeReset.e2Piece).toBe(null);

    expect(resetTest.afterReset.currentPlayer).toBe('white');
    expect(resetTest.afterReset.moveHistory).toBe(0);
    expect(resetTest.afterReset.e4Piece).toBe(null);
    expect(resetTest.afterReset.e2Piece).toBeTruthy();
  });

  test('should validate position notation conversion', async ({ page }) => {
    const notationTest = await page.evaluate(() => {
      const engine = window.chessGame.chessEngine;
      
      const tests = [
        { pos: 'a1', coords: engine.positionToCoords('a1') },
        { pos: 'h8', coords: engine.positionToCoords('h8') },
        { pos: 'e4', coords: engine.positionToCoords('e4') },
        { pos: 'd5', coords: engine.positionToCoords('d5') }
      ];
      
      const reverseTests = tests.map(test => ({
        coords: test.coords,
        pos: engine.coordsToPosition(test.coords.row, test.coords.col)
      }));
      
      return { tests, reverseTests };
    });

    expect(notationTest.tests[0].coords).toEqual({ row: 7, col: 0 }); // a1
    expect(notationTest.tests[1].coords).toEqual({ row: 0, col: 7 }); // h8
    expect(notationTest.tests[2].coords).toEqual({ row: 4, col: 4 }); // e4
    expect(notationTest.tests[3].coords).toEqual({ row: 3, col: 3 }); // d5

    expect(notationTest.reverseTests[0].pos).toBe('a1');
    expect(notationTest.reverseTests[1].pos).toBe('h8');
    expect(notationTest.reverseTests[2].pos).toBe('e4');
    expect(notationTest.reverseTests[3].pos).toBe('d5');
  });
});
