const { test, expect } = require('@playwright/test');

test.describe('Cross-Browser Compatibility', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.waitForSelector('#gameCanvas', { timeout: 10000 });
    await page.waitForSelector('#loadingScreen', { state: 'hidden', timeout: 15000 });
    await page.waitForTimeout(2000);
  });

  test('should work in all supported browsers', async ({ page, browserName }) => {
    console.log(`Testing in ${browserName}`);
    
    // Check basic functionality
    const canvas = page.locator('#gameCanvas');
    await expect(canvas).toBeVisible();
    
    const gameStatus = page.locator('#gameStatus');
    await expect(gameStatus).toContainText('White to move');
    
    // Test basic interaction
    await canvas.click({ position: { x: 400, y: 300 } });
    
    // Verify no critical errors
    const hasErrors = await page.evaluate(() => {
      return window.chessGame === undefined || window.chessGame.scene === null;
    });
    
    expect(hasErrors).toBe(false);
  });

  test('should handle WebGL support detection', async ({ page, browserName }) => {
    const webglInfo = await page.evaluate(() => {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      
      if (!gl) {
        return { supported: false };
      }
      
      return {
        supported: true,
        vendor: gl.getParameter(gl.VENDOR),
        renderer: gl.getParameter(gl.RENDERER),
        version: gl.getParameter(gl.VERSION)
      };
    });

    console.log(`WebGL support in ${browserName}:`, webglInfo);
    
    if (webglInfo.supported) {
      // If WebGL is supported, the game should work
      const sceneInitialized = await page.evaluate(() => {
        return window.chessGame && window.chessGame.scene && window.chessGame.scene.renderer;
      });
      
      expect(sceneInitialized).toBeTruthy();
    } else {
      // If WebGL is not supported, there should be a graceful fallback or error message
      console.log(`WebGL not supported in ${browserName}`);
    }
  });

  test('should handle different screen sizes', async ({ page }) => {
    const testSizes = [
      { width: 1920, height: 1080, name: 'Desktop Large' },
      { width: 1366, height: 768, name: 'Desktop Medium' },
      { width: 768, height: 1024, name: 'Tablet Portrait' },
      { width: 1024, height: 768, name: 'Tablet Landscape' },
      { width: 375, height: 667, name: 'Mobile Portrait' },
      { width: 667, height: 375, name: 'Mobile Landscape' }
    ];

    for (const size of testSizes) {
      console.log(`Testing ${size.name} (${size.width}x${size.height})`);
      
      await page.setViewportSize({ width: size.width, height: size.height });
      await page.waitForTimeout(500);
      
      // Check that UI is still accessible
      const gameUI = page.locator('#gameUI');
      await expect(gameUI).toBeVisible();
      
      const canvas = page.locator('#gameCanvas');
      await expect(canvas).toBeVisible();
      
      // Check that buttons are still clickable
      const newGameBtn = page.locator('#newGameBtn');
      await expect(newGameBtn).toBeVisible();
      
      // Test a click to ensure interaction still works
      await canvas.click({ position: { x: size.width / 2, y: size.height / 2 } });
    }
  });

  test('should handle different pixel ratios', async ({ page }) => {
    // Test different device pixel ratios
    const pixelRatios = [1, 1.5, 2, 3];
    
    for (const ratio of pixelRatios) {
      console.log(`Testing pixel ratio: ${ratio}`);
      
      await page.evaluate((ratio) => {
        // Override devicePixelRatio
        Object.defineProperty(window, 'devicePixelRatio', {
          writable: true,
          configurable: true,
          value: ratio
        });
        
        // Trigger resize to apply new pixel ratio
        if (window.chessGame && window.chessGame.scene) {
          window.chessGame.scene.handleResize();
        }
      }, ratio);
      
      await page.waitForTimeout(500);
      
      // Verify scene is still functional
      const sceneActive = await page.evaluate(() => {
        return window.chessGame && window.chessGame.scene && window.chessGame.scene.renderer;
      });
      
      expect(sceneActive).toBeTruthy();
    }
  });

  test('should handle performance on different devices', async ({ page }) => {
    // Simulate different performance levels
    const performanceTests = [
      { name: 'High Performance', cpuSlowdown: 1 },
      { name: 'Medium Performance', cpuSlowdown: 2 },
      { name: 'Low Performance', cpuSlowdown: 4 }
    ];

    for (const perfTest of performanceTests) {
      console.log(`Testing ${perfTest.name}`);
      
      // Apply CPU throttling
      const client = await page.context().newCDPSession(page);
      await client.send('Emulation.setCPUThrottlingRate', { rate: perfTest.cpuSlowdown });
      
      // Test game initialization time
      const startTime = Date.now();
      
      // Trigger a new game
      const newGameBtn = page.locator('#newGameBtn');
      await newGameBtn.click();
      
      // Wait for game to be ready
      await page.waitForTimeout(1000);
      
      const endTime = Date.now();
      const initTime = endTime - startTime;
      
      console.log(`${perfTest.name} init time: ${initTime}ms`);
      
      // Even on slow devices, should complete within reasonable time
      expect(initTime).toBeLessThan(10000);
      
      // Verify game is still functional
      const gameStatus = page.locator('#gameStatus');
      await expect(gameStatus).toContainText('White to move');
      
      // Reset CPU throttling
      await client.send('Emulation.setCPUThrottlingRate', { rate: 1 });
    }
  });

  test('should handle network conditions', async ({ page }) => {
    // Test with different network conditions
    const networkConditions = [
      { name: 'Fast 3G', downloadThroughput: 1.5 * 1024 * 1024 / 8, uploadThroughput: 750 * 1024 / 8, latency: 40 },
      { name: 'Slow 3G', downloadThroughput: 500 * 1024 / 8, uploadThroughput: 500 * 1024 / 8, latency: 400 }
    ];

    for (const condition of networkConditions) {
      console.log(`Testing ${condition.name}`);
      
      const client = await page.context().newCDPSession(page);
      await client.send('Network.emulateNetworkConditions', {
        offline: false,
        downloadThroughput: condition.downloadThroughput,
        uploadThroughput: condition.uploadThroughput,
        latency: condition.latency
      });
      
      // Reload page to test loading under network conditions
      await page.reload();
      await page.waitForSelector('#gameCanvas', { timeout: 30000 });
      await page.waitForSelector('#loadingScreen', { state: 'hidden', timeout: 30000 });
      
      // Verify game loaded successfully
      const gameStatus = page.locator('#gameStatus');
      await expect(gameStatus).toContainText('White to move');
      
      // Reset network conditions
      await client.send('Network.emulateNetworkConditions', {
        offline: false,
        downloadThroughput: -1,
        uploadThroughput: -1,
        latency: 0
      });
    }
  });

  test('should handle accessibility features', async ({ page }) => {
    // Test keyboard navigation
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    await page.keyboard.press('Enter'); // Should activate focused element
    
    // Test keyboard shortcuts
    await page.keyboard.press('Control+n'); // New game
    await page.keyboard.press('Control+r'); // Reset camera
    
    // Verify game is still functional
    const gameStatus = page.locator('#gameStatus');
    await expect(gameStatus).toContainText('White to move');
    
    // Check for basic accessibility attributes
    const canvas = page.locator('#gameCanvas');
    const canvasRole = await canvas.getAttribute('role');
    // Canvas might not have explicit role, which is acceptable for game canvas
    
    // Check that UI elements are properly labeled
    const newGameBtn = page.locator('#newGameBtn');
    const btnText = await newGameBtn.textContent();
    expect(btnText).toBeTruthy();
  });

  test('should handle browser-specific features', async ({ page, browserName }) => {
    console.log(`Testing browser-specific features for ${browserName}`);
    
    // Test touch events (important for mobile browsers)
    if (browserName === 'webkit') {
      // Safari-specific tests
      const canvas = page.locator('#gameCanvas');
      
      // Test touch events
      await canvas.dispatchEvent('touchstart', {
        touches: [{ clientX: 400, clientY: 300 }]
      });
      
      await canvas.dispatchEvent('touchend', {
        changedTouches: [{ clientX: 400, clientY: 300 }]
      });
    }
    
    // Test context menu prevention
    const canvas = page.locator('#gameCanvas');
    await canvas.click({ button: 'right' });
    
    // Verify no context menu appeared (game should prevent it)
    // This is hard to test directly, but we can verify the game is still functional
    const gameStatus = page.locator('#gameStatus');
    await expect(gameStatus).toContainText('White to move');
  });
});
